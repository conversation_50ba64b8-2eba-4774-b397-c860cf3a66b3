# Narissa Realty Platform - Real Estate CRM

A modern, AI-ready real estate CRM platform built with React, TypeScript, and Tailwind CSS.

## 🚀 Live Demo

[View Live Demo](https://crm-demo-delta.vercel.app)

**Demo Credentials:**
- Email: `<EMAIL>`
- Password: `demo123`

## ✨ Features

- **Client Management**: Track and manage 42+ real client profiles
- **Property Listings**: Browse 100+ MLS properties with photos
- **Smart Dashboard**: Real-time metrics and activity tracking
- **AI-Ready Architecture**: Prepared for intelligent property matching
- **Responsive Design**: Works seamlessly on desktop and mobile
- **Mock Authentication**: Secure login system demonstration

## 🛠 Technology Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, Shadcn/ui components
- **Routing**: React Router v6
- **State Management**: React Context API
- **Build Tool**: Vite
- **Deployment**: Vercel

## 📦 Installation

```bash
# Clone the repository
git clone https://github.com/endersclarity/NarissaRealtyCRM.git

# Navigate to project directory
cd NarissaRealtyCRM

# Install dependencies
npm install

# Start development server
npm run dev
```

## 🏗 Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## 📁 Project Structure

```
src/
├── components/     # Reusable UI components
├── contexts/       # React Context providers
├── data/          # Mock data (clients, properties)
├── lib/           # Utilities and helpers
├── pages/         # Route components
└── styles/        # Global styles
```

## 🎯 Key Pages

- **Dashboard** - Overview of metrics and activities
- **Clients** - Manage client relationships
- **Properties** - Browse MLS listings with advanced filters
- **Schedule** - Appointment management
- **Documents** - File storage interface
- **Reports** - Analytics and insights

## 🔮 Future Enhancements

- Real-time property matching AI
- Automated client communications
- Market analysis tools
- Transaction management
- Mobile app companion

## 📄 License

This project is created for portfolio demonstration purposes.

---

Built with ❤️ for modern real estate professionals

## Lovable Project Info

**Original URL**: https://lovable.dev/projects/5b082324-145c-493c-9d51-b3cf32357e77