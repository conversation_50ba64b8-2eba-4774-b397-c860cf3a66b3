// Direct test of AI service without going through HTTP
import { aiService } from './src/services/aiService';

const testCases = [
  {
    query: "harris",
    shouldFind: ["<PERSON>"],
    shouldNotFind: ["<PERSON>", "<PERSON>"],
    description: "Search by last name only"
  },
  {
    query: "jennings",
    shouldFind: [],
    shouldSuggest: "couldn't find",
    description: "Non-existent name should say couldn't find"
  },
  {
    query: "bass property",
    shouldFind: ["Bass Trail"],
    description: "Property search by street name"
  },
  {
    query: "show me aaron mc<PERSON><PERSON>",
    shouldFind: ["<PERSON>"],
    shouldNotFind: ["<PERSON>"],
    description: "Exact name match"
  }
];

async function runTests() {
  console.log("🧪 Testing AI Search Directly\n");
  
  for (const test of testCases) {
    console.log(`\nTest: ${test.description}`);
    console.log(`Query: "${test.query}"`);
    
    try {
      const response = await aiService.sendMessage(test.query, []);
      const message = response.message;
      
      console.log(`Response: ${message.substring(0, 150)}...`);
      
      // Validate response
      let passed = true;
      
      if (test.shouldFind) {
        for (const item of test.shouldFind) {
          if (!message.toLowerCase().includes(item.toLowerCase())) {
            console.log(`❌ Missing: "${item}"`);
            passed = false;
          }
        }
      }
      
      if (test.shouldNotFind) {
        for (const item of test.shouldNotFind) {
          if (message.toLowerCase().includes(item.toLowerCase())) {
            console.log(`❌ Should not have: "${item}"`);
            passed = false;
          }
        }
      }
      
      if (test.shouldSuggest && !message.toLowerCase().includes(test.shouldSuggest)) {
        console.log(`❌ Should include: "${test.shouldSuggest}"`);
        passed = false;
      }
      
      console.log(passed ? "✅ PASSED" : "❌ FAILED");
      
    } catch (error) {
      console.log(`❌ ERROR: ${error}`);
    }
    
    // Small delay
    await new Promise(r => setTimeout(r, 500));
  }
}

// Wait for service to initialize
setTimeout(() => {
  runTests().catch(console.error);
}, 1000);