#!/bin/bash
# Run the staging container

echo "🚀 Starting staging container..."
echo "This is your self-contained, production-ready CRM"
echo ""

# Check if staging container exists
if ! docker images | grep -q "lovable-crm-staging"; then
    echo "❌ Staging container not found!"
    echo "Run ./staging-build.sh first"
    exit 1
fi

# Stop any existing staging container
docker stop lovable-crm-staging 2>/dev/null
docker rm lovable-crm-staging 2>/dev/null

# Run the staging container
echo "Starting container on http://localhost:5003"
docker run -d \
    --name lovable-crm-staging \
    -p 5003:4173 \
    lovable-crm-staging:latest

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Staging container running!"
    echo "🌐 Access at: http://localhost:5003"
    echo ""
    echo "To stop: docker stop lovable-crm-staging"
    echo "To see logs: docker logs lovable-crm-staging"
else
    echo "❌ Failed to start container!"
    exit 1
fi