#!/usr/bin/env node

// Test script for AI search functionality
// Tests various search queries and validates responses

const testCases = [
  {
    query: "harris",
    shouldFind: ["<PERSON>"],
    shouldNotFind: ["<PERSON>", "<PERSON>"],
    description: "Search by last name only"
  },
  {
    query: "micha<PERSON>",
    shouldFind: ["<PERSON>", "<PERSON>", "<PERSON>"],
    shouldNotFind: [],
    description: "Search by first name"
  },
  {
    query: "jennings",
    shouldFind: [],
    shouldSuggest: true,
    description: "Non-existent name should suggest alternatives"
  },
  {
    query: "bass property",
    shouldFind: ["13383 Bass Trail"],
    shouldNotFind: [],
    description: "Property search by street name"
  },
  {
    query: "properties in davis",
    shouldFind: ["<PERSON>"],
    shouldNotFind: [],
    description: "Property search by city"
  },
  {
    query: "show me aaron mcdaniel",
    shouldFind: ["<PERSON>"],
    shouldNotFind: ["<PERSON>"],
    description: "Exact name match (case insensitive)"
  }
];

async function testAISearch() {
  console.log("🧪 Testing AI Search Functionality\n");
  
  let passed = 0;
  let failed = 0;
  
  for (const test of testCases) {
    console.log(`\n📝 Test: ${test.description}`);
    console.log(`   Query: "${test.query}"`);
    
    try {
      const response = await fetch('http://localhost:5002/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: test.query,
          conversationHistory: []
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      const aiResponse = data.message || data.response || '';
      
      console.log(`   Response: ${aiResponse.substring(0, 100)}...`);
      
      // Check if expected items were found
      let testPassed = true;
      
      if (test.shouldFind) {
        for (const item of test.shouldFind) {
          if (!aiResponse.toLowerCase().includes(item.toLowerCase())) {
            console.log(`   ❌ Should find "${item}" but didn't`);
            testPassed = false;
          }
        }
      }
      
      if (test.shouldNotFind) {
        for (const item of test.shouldNotFind) {
          if (aiResponse.toLowerCase().includes(item.toLowerCase())) {
            console.log(`   ❌ Should NOT find "${item}" but did`);
            testPassed = false;
          }
        }
      }
      
      if (test.shouldSuggest && !aiResponse.toLowerCase().includes("couldn't find")) {
        console.log(`   ❌ Should say "couldn't find" but didn't`);
        testPassed = false;
      }
      
      if (testPassed) {
        console.log(`   ✅ PASSED`);
        passed++;
      } else {
        failed++;
      }
      
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
      failed++;
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log(`\n📊 Results: ${passed} passed, ${failed} failed`);
  
  if (failed > 0) {
    console.log("\n🔧 Suggested fixes:");
    console.log("1. Check if search is too restrictive");
    console.log("2. Verify property search includes address field");
    console.log("3. Ensure AI has access to search results");
    console.log("4. Check for case sensitivity issues");
  }
}

// Check if API endpoint exists first
fetch('http://localhost:5002/api/ai/chat', { method: 'OPTIONS' })
  .then(() => testAISearch())
  .catch(() => {
    console.log("❌ API endpoint not found. Looking for correct endpoint...");
    
    // Try to find the correct endpoint
    console.log("\nTrying to identify the correct AI endpoint...");
    console.log("Check the network tab in browser dev tools when using the chat.");
  });