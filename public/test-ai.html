<!DOCTYPE html>
<html>
<head>
    <title>AI Search Test Suite</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #1a1a1a; color: #fff; }
        .test { margin: 20px 0; padding: 10px; border: 1px solid #444; }
        .passed { background: #1a3a1a; }
        .failed { background: #3a1a1a; }
        button { padding: 10px 20px; font-size: 16px; cursor: pointer; }
        #results { margin-top: 20px; }
        .query { color: #4a9eff; }
        .response { color: #aaa; font-style: italic; }
        .error { color: #ff4444; }
        .success { color: #44ff44; }
    </style>
</head>
<body>
    <h1>🧪 AI Search Test Suite</h1>
    <button onclick="runAllTests()">Run All Tests</button>
    <button onclick="runCustomTest()">Run Custom Test</button>
    <input type="text" id="customQuery" placeholder="Enter custom query" style="margin-left: 10px; padding: 8px;">
    
    <div id="results"></div>

    <script type="module">
        // Import the AI service
        import { aiService } from '/src/services/aiService.ts';
        
        window.aiService = aiService;
        
        const testCases = [
            {
                query: "harris",
                shouldFind: ["Michael Harris"],
                shouldNotFind: ["Sarah Harris", "Bob Harris"],
                description: "Search by last name only"
            },
            {
                query: "jennings",
                shouldFind: [],
                shouldInclude: "couldn't find",
                description: "Non-existent name"
            },
            {
                query: "bass property",
                shouldFind: ["Bass Trail", "13383"],
                description: "Property by street name"
            },
            {
                query: "aaron mcdaniel",
                shouldFind: ["Aaron Mcdaniel"],
                shouldNotFind: ["McDaniels"],
                description: "Case variations"
            },
            {
                query: "properties in davis",
                shouldFind: ["Davis"],
                description: "Properties by city"
            },
            {
                query: "list all clients",
                shouldFind: ["Michael Harris", "Aaron"],
                shouldNotFind: ["[Name", "not found"],
                description: "List all clients"
            }
        ];
        
        window.runAllTests = async function() {
            const results = document.getElementById('results');
            results.innerHTML = '<h2>Running tests...</h2>';
            
            let html = '';
            let passed = 0;
            let failed = 0;
            
            for (const test of testCases) {
                const result = await runSingleTest(test);
                
                html += `
                    <div class="test ${result.passed ? 'passed' : 'failed'}">
                        <h3>${test.description}</h3>
                        <div class="query">Query: "${test.query}"</div>
                        <div class="response">Response: ${result.response.substring(0, 200)}...</div>
                        ${result.errors.map(e => `<div class="error">❌ ${e}</div>`).join('')}
                        ${result.passed ? '<div class="success">✅ PASSED</div>' : ''}
                    </div>
                `;
                
                if (result.passed) passed++;
                else failed++;
            }
            
            results.innerHTML = `
                <h2>Results: ${passed} passed, ${failed} failed</h2>
                ${html}
            `;
        };
        
        window.runCustomTest = async function() {
            const query = document.getElementById('customQuery').value;
            if (!query) return;
            
            const results = document.getElementById('results');
            
            try {
                const response = await aiService.sendMessage(query, []);
                results.innerHTML = `
                    <div class="test">
                        <h3>Custom Query</h3>
                        <div class="query">Query: "${query}"</div>
                        <div class="response">Response: ${response.message}</div>
                    </div>
                `;
            } catch (error) {
                results.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        };
        
        async function runSingleTest(test) {
            const errors = [];
            let response = '';
            
            try {
                const result = await aiService.sendMessage(test.query, []);
                response = result.message;
                
                // Check expectations
                if (test.shouldFind) {
                    for (const item of test.shouldFind) {
                        if (!response.toLowerCase().includes(item.toLowerCase())) {
                            errors.push(`Should find "${item}" but didn't`);
                        }
                    }
                }
                
                if (test.shouldNotFind) {
                    for (const item of test.shouldNotFind) {
                        if (response.toLowerCase().includes(item.toLowerCase())) {
                            errors.push(`Should NOT find "${item}" but did`);
                        }
                    }
                }
                
                if (test.shouldInclude) {
                    if (!response.toLowerCase().includes(test.shouldInclude.toLowerCase())) {
                        errors.push(`Should include "${test.shouldInclude}" but didn't`);
                    }
                }
                
            } catch (error) {
                errors.push(`Exception: ${error.message}`);
                response = 'ERROR';
            }
            
            return {
                passed: errors.length === 0,
                errors,
                response
            };
        }
        
        // Auto-run tests after 2 seconds
        setTimeout(() => {
            console.log('AI Service initialized:', aiService.isInitialized());
            window.runAllTests();
        }, 2000);
    </script>
</body>
</html>