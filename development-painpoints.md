# Development Pain Points - Lovable Design Project

This file tracks project-specific recurring issues and their solutions to speed up future debugging.

## Template for New Pain Points
```
## [Issue Name]

**Keywords:** `[error terms]`, `[file names]`, `[symptoms]`

**Error Symptoms:**
- Exact error messages
- Observable behaviors

**What DOESN'T Work:**
- Failed solutions attempted
- Why they don't work

**What WORKS:**
- Exact solution with commands
- Root cause explanation

**Quick Fix Command:**
- One-liner instruction

**Time to Resolution:** [before] → [target]
```

---

## AI Service Deployment/HMR Cache Hell

**Keywords:** `aiService.ts`, `changes not reflected`, `stale code`, `docker hmr`, `hallucination`, `basic search fails`

**Error Symptoms:**
- AI code changes made but never appear in live responses
- AI hallucinating fake data instead of using database search
- Basic search for "bass trail" returns made-up "123 Bass Trail, Austin TX" instead of real "13383 Bass Trail, Grass Valley"
- Debug output added to code never appears in AI responses
- Container HMR logs show updates for other files but NOT aiService.ts
- Hours of code changes with zero functional impact

**What DOESN'T Work:**
- Editing aiService.ts directly - changes don't deploy
- Adding debug console.log statements - never execute
- Restarting with docker touch commands - no effect
- Complex search logic fixes - code never runs
- HMR expecting to pick up aiService.ts changes automatically

**Root Cause:**
AI service file changes are not being deployed to live site due to:
- Possible TypeScript compilation cache issues
- Docker container not watching aiService.ts properly
- Server-side API caching preventing code updates
- Alternative AI endpoint being used instead of local file

**Impact:**
- Completely blocks AI functionality development
- Makes debugging impossible (can't see if changes work)
- Leads to false debugging cycles (fixing code that isn't running)
- Prevents basic database search that should take 10 minutes

**What WORKS:**
- Unknown - deployment issue not resolved
- Other files (React components) update via HMR correctly
- Direct database access works fine in Properties page

**Quick Fix Command:**
- **TODO: Find deployment solution**

**Business Impact:**
Prevents implementation of core AI vision: parsing messy communications (texts/emails/calls) to intelligently update CRM data. Entire AI feature set blocked by deployment issues.

**Time to Resolution:** 4+ hours wasted → **UNRESOLVED**

---

## Sidebar Click Issues

**Keywords:** `sidebar navigation`, `clicks not registering`, `LuxurySidebar.tsx`, `pointer-events`

**Error Symptoms:**
- Clicking sidebar navigation items doesn't navigate
- Click events seem to be blocked or intercepted
- Console shows no errors

**What DOESN'T Work:**
- Adding onClick handlers directly
- z-index adjustments alone

**What WORKS:**
```css
/* In LuxurySidebar.tsx styles */
pointer-events: auto;
position: relative;
z-index: 10;
```

**Quick Fix Command:**
"Apply CSS pointer-events workaround in LuxurySidebar.tsx"

**Time to Resolution:** Multiple occurrences → <5 minutes with this reference

**Notes:** This is a recurring issue that appears after certain UI updates. Root cause unclear but CSS workaround consistently fixes it.

---

## Container Port Mismatch - Vite Config vs Docker Mapping

**Keywords:** `docker container`, `vite`, `port 5002`, `lovable-ui-demo`, `connection reset`, `site can't be reached`

**Error Symptoms:**
- Container lovable-ui-demo runs but localhost:5002 returns "site can't be reached"
- Vite says "ready" but curl returns "Connection reset by peer"
- Container maps external 5002 to internal 8080 (0.0.0.0:5002->8080/tcp)
- Vite configured to serve on port 5002 inside container

**What DOESN'T Work:**
- Running npm dev directly in WSL2 (not using container)
- Changing vite.config.ts port without considering container mapping
- Assuming vite "ready" message means it's accessible

**What WORKS:**
```bash
# 1. Check which container should be running
docker ps -a | grep -E "(offer|crm|lovable)"

# 2. Ensure vite.config.ts port matches INTERNAL container port
# If container maps 5002->8080, vite must use port: 8080

# 3. Start the correct container
docker start lovable-ui-demo

# 4. Verify with curl after ~10 seconds
curl -I http://localhost:5002
```

**Root Cause:** 
Container port mapping (external:internal) must match vite's server port configuration. If container maps 5002->8080, vite must serve on 8080 internally.

**Quick Fix Command:**
"Set vite port to match container's INTERNAL port mapping"

**Time to Resolution:** 15+ minutes of confusion → <2 minutes with proper understanding

**User Conversation Context:**
User was extremely frustrated when I tried running npm dev directly instead of using the Docker container. Wasted time with permission errors when the real issue was not using the container at all, then port mismatch when container was started.

---

## AI Search Implementation - Complete Shitshow

**Keywords:** `AI hallucination`, `search not working`, `aiService.ts`, `responseValidator`, `grounded generation`, `RAG`

**Error Symptoms:**
- AI creating fake client names ("Michael Jennings" instead of no results)
- Real searches failing ("harris" → "I couldn't find any clients")
- Property searches not working ("bass property" → error)
- Response validator destroying legitimate output
- Generic "I encountered an error" messages

**What DOESN'T Work:**
- Complex regex patterns for name extraction
- Overly strict validation rules
- Post-generation response validation (caught "View Profile" as fake name)
- Narrow search criteria (only specific fields)
- Human-in-the-loop testing (too slow)

**What WORKS:**
```typescript
// Simple, broad search approach:
// 1. Cast wide net - search on any single/two word input
// 2. Simple keyword extraction - no complex regex
// 3. Graceful error handling with context
// 4. Let AI work with search results, don't pre-filter
```

**Root Cause:**
Over-engineered search logic trying to be too clever. Claude's approach works because it's SIMPLE - cast wide net, fail gracefully, don't overthink.

**Quick Fix Command:**
"Simplify search logic, disable aggressive validation, test programmatically"

**Time to Resolution:** 2+ hours of back-and-forth → Should be 15 minutes with automated testing

**Lesson Learned:**
Should test AI responses programmatically instead of human-in-the-loop. Can directly call API and verify responses.

---