const fs = require('fs');

// Function to normalize phone numbers to (XXX) XXX-XXXX format
function normalizePhone(phone) {
  if (!phone) return null;
  
  // Extract just the digits
  const digits = phone.toString().replace(/\D/g, '');
  
  // Take only the first 10 digits (ignore extensions)
  const cleanDigits = digits.slice(0, 10);
  
  // If we don't have exactly 10 digits, generate a random valid phone
  if (cleanDigits.length !== 10) {
    // Generate random phone with 555 prefix (standard for fake numbers)
    const area = Math.floor(Math.random() * 800) + 200; // 200-999
    return `(${area}) 555-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
  }
  
  // Format as (XXX) XXX-XXXX
  return `(${cleanDigits.slice(0, 3)}) ${cleanDigits.slice(3, 6)}-${cleanDigits.slice(6)}`;
}

// Read and process clients data
console.log('Processing client phone numbers...');
const clientsFile = fs.readFileSync('src/data/realClientsData.ts', 'utf8');
let clientsData = clientsFile;

// Find all phone numbers and normalize them
const phoneRegex = /"mobile_phone": "([^"]+)"/g;
let match;
const phoneReplacements = [];

while ((match = phoneRegex.exec(clientsFile)) !== null) {
  const originalPhone = match[1];
  const normalizedPhone = normalizePhone(originalPhone);
  if (originalPhone !== normalizedPhone) {
    phoneReplacements.push({ original: originalPhone, normalized: normalizedPhone });
  }
}

// Apply phone replacements
phoneReplacements.forEach(({ original, normalized }) => {
  clientsData = clientsData.replace(
    `"mobile_phone": "${original}"`,
    `"mobile_phone": "${normalized}"`
  );
});

// Write updated clients data
fs.writeFileSync('src/data/realClientsData.ts', clientsData);
console.log(`Normalized ${phoneReplacements.length} phone numbers`);

// Read properties data
console.log('\nProcessing properties...');
const propertiesFile = fs.readFileSync('src/data/realPropertiesData.ts', 'utf8');

// Remove properties with placeholder images
const propertyIdsToRemove = [100020, 100022, 100074, 100085, 100091];
let propertiesData = propertiesFile;

// Count properties before
const beforeCount = (propertiesData.match(/{\s*"id":/g) || []).length;

propertyIdsToRemove.forEach(id => {
  // Remove the entire property object
  const regex = new RegExp(`\\{\\s*"id":\\s*${id},[^}]*\\},?`, 'gs');
  propertiesData = propertiesData.replace(regex, '');
});

// Clean up any double commas or trailing commas
propertiesData = propertiesData.replace(/,\s*,/g, ',');
propertiesData = propertiesData.replace(/,\s*\]/g, ']');

// Count properties after
const afterCount = (propertiesData.match(/{\s*"id":/g) || []).length;

// Write updated properties data
fs.writeFileSync('src/data/realPropertiesData.ts', propertiesData);
console.log(`Removed ${beforeCount - afterCount} properties with placeholder images`);

console.log('\nCleanup complete!');