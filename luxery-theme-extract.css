@import "tailwindcss";

/* Luxury Real Estate Theme for Next.js + Tailwind v4 */
/* Extracted from Lovable design and adapted */

:root {
  /* OpenAI-inspired luxury theme with glassmorphism */
  --background: #f8fafc; /* Light cream background */
  --foreground: #232941; /* Deep navy text */
  
  /* Luxury Real Estate Color Palette */
  --luxury-navy: #1a2852;
  --luxury-navy-light: #2c3968;
  --luxury-gold: #d4af37;
  --luxury-gold-light: #e6c754;
  --luxury-cream: #f8f6f0;
  --luxury-silver: #c0c0c0;
  
  /* Glass Effects */
  --glass-white: rgba(255, 255, 255, 0.1);
  --glass-white-strong: rgba(255, 255, 255, 0.2);
  --glass-dark: rgba(0, 0, 0, 0.1);
  
  /* Shadcn/UI Compatible Variables */
  --primary: #2563eb; /* OpenAI blue */
  --primary-foreground: #f8fafc;
  --secondary: #f1f5f9;
  --secondary-foreground: #232941;
  --muted: #e2e8f0;
  --muted-foreground: #64748b;
  --accent: #dbeafe;
  --accent-foreground: #232941;
  --destructive: #dc2626;
  --destructive-foreground: #f8fafc;
  --border: #e0e7ef;
  --input: #e0e7ef;
  --ring: #2563eb;
  --radius: 0.75rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-luxury-navy: var(--luxury-navy);
  --color-luxury-navy-light: var(--luxury-navy-light);
  --color-luxury-gold: var(--luxury-gold);
  --color-luxury-gold-light: var(--luxury-gold-light);
  --color-luxury-cream: var(--luxury-cream);
  --color-luxury-silver: var(--luxury-silver);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --border-radius: var(--radius);
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
}

/* Glassmorphism Utility Classes */
.glass {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.glass-subtle {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
}

/* Luxury Gradient Backgrounds */
.luxury-gradient {
  background: linear-gradient(135deg, var(--luxury-navy) 0%, var(--luxury-navy-light) 50%, var(--luxury-navy) 100%);
}

.gold-gradient {
  background: linear-gradient(135deg, var(--luxury-gold) 0%, var(--luxury-gold-light) 50%, var(--luxury-gold) 100%);
}

.glass-gradient {
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
}

/* Animation Classes */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.3); 
  }
  50% { 
    box-shadow: 0 0 40px rgba(212, 175, 55, 0.5); 
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.2s ease-out;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

/* Professional Component Styles */
.primary-action {
  background: var(--primary);
  color: var(--primary-foreground);
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: var(--radius);
  box-shadow: 0 4px 14px rgba(37, 99, 235, 0.3);
  transition: all 0.2s ease;
}

.primary-action:hover {
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
  transform: translateY(-1px);
}

.luxury-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius);
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(26, 40, 82, 0.1);
  transition: all 0.3s ease;
}

.luxury-card:hover {
  box-shadow: 0 12px 48px rgba(26, 40, 82, 0.15);
  transform: translateY(-2px);
}

/* Typography */
.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--luxury-navy);
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--luxury-navy-light);
  margin-bottom: 1rem;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--luxury-navy);
}

.metric-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--luxury-navy-light);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}