// Test the search logic directly
const fs = require('fs');

// Read and parse the data files
const clientsFile = fs.readFileSync('./src/data/realClientsData.ts', 'utf8');
const propertiesFile = fs.readFileSync('./src/data/realPropertiesData.ts', 'utf8');

// Extract the data arrays
const clientsMatch = clientsFile.match(/export const realClientsData[^=]*=\s*(\[[\s\S]*?\]);/);
const propertiesMatch = propertiesFile.match(/export const realPropertiesData[^=]*=\s*(\[[\s\S]*?\]);/);

const realClientsData = eval(clientsMatch[1]);
const realPropertiesData = eval(propertiesMatch[1]);

console.log('🔍 Testing Search Logic\n');

// Test 1: Search for "bass" in properties
console.log('Test 1: Properties with "bass" in address');
const bassProperties = realPropertiesData.filter(p => 
  p.address_street_complete.toLowerCase().includes('bass')
);
console.log(`Found ${bassProperties.length} properties:`);
bassProperties.forEach(p => {
  console.log(`  - ${p.address_street_complete}, ${p.address_city} - $${(parseFloat(p.list_price)/1000).toFixed(0)}k`);
});

// Test 2: Search for clients with last name "harris"
console.log('\nTest 2: Clients with last name "harris"');
const harrisClients = realClientsData.filter(c => 
  c.last_name.toLowerCase().includes('harris')
);
console.log(`Found ${harrisClients.length} clients:`);
harrisClients.forEach(c => {
  console.log(`  - ${c.first_name} ${c.last_name} (${c.city || 'Unknown'})`);
});

// Test 3: Properties in Davis
console.log('\nTest 3: Properties in Davis');
const davisProperties = realPropertiesData.filter(p => 
  p.address_city.toLowerCase() === 'davis'
);
console.log(`Found ${davisProperties.length} properties in Davis`);

// Test 4: Check if "jennings" exists
console.log('\nTest 4: Clients with "jennings" in name');
const jenningsClients = realClientsData.filter(c => 
  c.first_name.toLowerCase().includes('jennings') || 
  c.last_name.toLowerCase().includes('jennings')
);
console.log(`Found ${jenningsClients.length} clients with "jennings"`);

// Test 5: Count all clients
console.log('\nTest 5: Total client count');
console.log(`Total clients: ${realClientsData.length}`);
console.log('First 5 clients:');
realClientsData.slice(0, 5).forEach(c => {
  console.log(`  - ${c.first_name} ${c.last_name}`);
});

// Test 6: Verify Aaron exists
console.log('\nTest 6: Clients named Aaron');
const aaronClients = realClientsData.filter(c => 
  c.first_name.toLowerCase() === 'aaron'
);
console.log(`Found ${aaronClients.length} Aarons:`);
aaronClients.forEach(c => {
  console.log(`  - ${c.first_name} ${c.last_name}`);
});