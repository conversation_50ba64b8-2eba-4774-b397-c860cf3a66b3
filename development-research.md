# Development Research

This file documents in-depth research, architectural explorations, and problem-solving approaches for complex issues encountered during development.

---

## [2025-06-18] AI Search Architecture - RAG Implementation Research

### Problem Statement
- **Issue**: AI hallucinating client names (e.g., "<PERSON>" when only "<PERSON>" exists)
- **Symptom**: Inconsistent behavior - saying "no clients with name <PERSON>", then finding "<PERSON>"
- **Root Cause**: AI attempting to answer questions WITHOUT searching the database first

### Research Conducted

#### 1. Context7 Search - RAG Implementation Patterns
- **Found**: `/danny-avila/rag_api` - Basic RAG with PostgreSQL/pgvector
- **Found**: `/langchain-ai/langgraphjs-api` - LangGraph patterns for structured workflows

#### 2. Web Search - RAG Best Practices
- **RAG Definition**: Retrieval Augmented Generation - optimizing LLM output by referencing authoritative knowledge base before generating response
- **Key Process**: Load → Split → Store → Retrieve → Generate
- **Core Benefit**: "RAG ensures the model has access to the most current, reliable facts"
- **Trust Factor**: Provides sources that can be cited, like footnotes in research paper

**Key RAG Components**:
1. **Retrieval Phase**: Algorithms search for and retrieve snippets of information relevant to the user's prompt
2. **Generation Phase**: LLM draws from augmented prompt and training data to synthesize answer

#### 3. Fuzzy vs Exact Matching Research
- **Exact Match**: Ideal for structured data with consistency (e.g., primary keys, IDs)
- **Fuzzy Match**: Accounts for variations (typos, similar spellings)
- **Best Practice**: Hybrid approach - exact first, fuzzy for "did you mean?" suggestions
- **CRM Context**: Fuzzy matching helps identify duplicates, but can create confusion if not controlled

### Key Findings - Why Current Approach Fails

1. **No Search-First Architecture**: AI generates responses before searching database
2. **No Clear Phase Separation**: Search and generation happen simultaneously/randomly
3. **Aggressive Fuzzy Matching**: entityRouteMapper links to wrong people
4. **Weak Prompt Constraints**: System prompt doesn't enforce "grounded generation"

### Proposed Solution: Proper RAG Implementation

#### Current vs Correct Flow
```
CURRENT: User → AI (guesses) → Maybe searches → Response (with hallucinations)
CORRECT: User → Search Database → Provide Results to AI → AI uses ONLY results → Response
```

#### Architecture Changes Required

1. **Two-Phase Approach**:
   ```typescript
   // Phase 1: Search
   const searchIntent = extractSearchIntent(userMessage);
   const searchResults = performDatabaseSearch(searchIntent);
   
   // Phase 2: Generate (with results)
   const response = generateWithResults(userMessage, searchResults);
   ```

2. **Search Intent Detection**:
   ```typescript
   interface SearchIntent {
     type: 'client' | 'property' | 'none';
     criteria: {
       name?: string;
       lastName?: string;
       city?: string;
       priceRange?: { min?: number; max?: number };
     };
     confidence: number;
   }
   ```

3. **Enhanced Prompt Structure**:
   ```
   SEARCH RESULTS FROM DATABASE:
   - Michael Harris (investor, Sacramento, ID: 5)
   
   USER QUESTION: Find clients with last name Harris
   
   INSTRUCTIONS: 
   - Answer based ONLY on the search results above
   - If no results shown, say "I found no clients matching [criteria]"
   - Only create links for exact names from search results
   ```

### Best Practices from Research

1. **Data Preparation**: Clean, chunk, and index data appropriately
2. **Hybrid Search**: Combine vector search with traditional text search
3. **Quality Control**: Always provide sources for verification
4. **Performance**: Balance accuracy vs speed - exact match first, fuzzy as fallback
5. **Error Handling**: Clear messages when no results found

### Implementation Strategy

1. **Refactor `aiService.ts`**:
   - Separate search logic from AI generation
   - Always search before invoking AI
   - Pass search results explicitly to AI

2. **Update System Prompts**:
   - Enforce result-based responses
   - Prohibit hallucination
   - Require explicit "not found" messages

3. **Fix Link Generation**:
   - Only create links for exact matches
   - Validate all links before rendering

### Perplexity Research Prompt

```
I need to implement a RAG (Retrieval Augmented Generation) system for a small CRM database 
with 30 clients and 95 properties. The AI keeps hallucinating names that don't exist 
(e.g., saying "Michael Jennings" exists when only "Michael Harris" is in the database).

Please research:
1. Best practices for RAG systems with small, structured datasets
2. System prompt templates that enforce "grounded generation" (only mentioning data that was actually retrieved)
3. Examples of search-first architectures where the system queries the database BEFORE the LLM generates a response
4. Techniques to prevent hallucination in limited-domain AI assistants
5. Structured prompt templates for CRM/database search assistants

Focus on practical implementation patterns, especially for systems using Google Gemini or similar LLMs with TypeScript/JavaScript.
```

### Key Lessons Learned

1. **Ad-hoc fixes don't solve architectural problems** - Tweaking prompts won't fix a missing search layer
2. **RAG is the established pattern** - Don't reinvent the wheel
3. **Search MUST precede generation** - This is non-negotiable for accuracy
4. **AI should present, not guess** - The AI's role is to format and present search results, not imagine what might exist
5. **Hallucination is preventable** - With proper architecture, not just prompt engineering

#### 4. Perplexity Research Results - Production-Grade RAG Insights

**Effectiveness Metrics from Industry Research**:
- **Search-first architecture**: 92% effectiveness rate in preventing hallucinations
- **Grounded generation prompts**: 85% reduction in hallucination when properly implemented
- **Key finding**: For small datasets (like our 30 clients), precision is more important than recall

**Anti-Hallucination Techniques Ranking**:
1. **Real-Time Database Queries** (88% effective) - Always query fresh data, no caching
2. **Grounded Generation Prompts** (85% effective) - Explicit constraints in system prompt
3. **Response Validation** (80% effective) - Post-generation checking of all entities
4. **Source Citation Requirements** (78% effective) - Forces AI to reference actual data

**Critical Template Pattern for Grounded Generation**:
```
You are a CRM assistant. You can ONLY provide information that is explicitly 
found in the search results below. If the information is not in the search 
results, you must say "I don't have that information in the database."

SEARCH RESULTS:
[Actual database results here]

STRICT RULES:
- Only mention names that appear in the search results
- Never create or guess client names
- If no results found, say so explicitly
- All statements must be traceable to search results
```

**Industry Best Practices**:
- **Hybrid search** combining SQL + semantic search (overkill for our structured data)
- **10-week implementation timeline** for enterprise RAG (we can do this in hours)
- **Monitoring thresholds**: Alert if hallucination rate exceeds 10%
- **Small dataset insight**: Better to acknowledge missing data than fabricate

### Simplified Implementation for Our Use Case

After analyzing the research, our current implementation is actually ~80% correct. We already have:
- ✅ Database search functionality (`searchClients`, `searchProperties`)
- ✅ Search results being passed to AI (`dataContext`)
- ✅ Exact matching in `entityRouteMapper`
- ✅ Search happening before AI generation

**What we're missing**:
1. **Stricter prompt enforcement** - Current prompt allows too much creative freedom
2. **Pre-generation validation** - Should skip AI entirely if no search results
3. **Post-generation validation** - Need to verify all mentioned names exist

**Practical fixes needed**:
```typescript
// 1. Pre-generation check
if (searchResults.length === 0) {
  return { 
    message: `I couldn't find any clients matching "${searchTerm}" in the database.`,
    intent: 'search'
  };
}

// 2. Stricter system prompt
const GROUNDED_PROMPT = `
CRITICAL: You can ONLY mention clients that appear in the "SEARCH RESULTS" section.
If a name is not in the search results, you MUST NOT mention it.
Instead say: "I don't have information about [name] in the database."
`;

// 3. Post-generation validation
function validateResponse(response: string, validNames: string[]): string {
  // Check if response mentions any names not in validNames
  // If found, replace with "I don't have information about X"
}
```

**Why this works for us**:
- Small dataset (30 clients) makes exact matching practical
- Structured data doesn't need complex vector embeddings
- Current search logic just needs stricter constraints
- No need for enterprise-grade RAG infrastructure

---

## Future Research Topics

- [ ] Vector embeddings for semantic search in small datasets
- [ ] Caching strategies for repeated queries
- [ ] User feedback loops for search improvement
- [ ] Multi-modal search (combining text, filters, and context)