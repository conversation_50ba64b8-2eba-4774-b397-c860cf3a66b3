# Deployment Instructions for Vercel

## Prerequisites
- Vercel account (free tier is sufficient)
- Git repository pushed to GitHub/GitLab/Bitbucket

## Steps to Deploy

1. **Push to Remote Repository**
   ```bash
   git remote add origin YOUR_REPO_URL
   git push -u origin main
   ```

2. **Deploy to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your Git repository
   - Configure build settings:
     - Framework Preset: Vite
     - Build Command: `npm run build`
     - Output Directory: `dist`
     - Install Command: `npm install`

3. **Environment Variables**
   No environment variables are required for this demo app.
   All data is mocked locally.

4. **Deployment Configuration**
   The `vercel.json` file is already configured for SPA routing:
   ```json
   {
     "rewrites": [
       { "source": "/(.*)", "destination": "/index.html" }
     ]
   }
   ```

## Post-Deployment

1. Your app will be available at:
   - `https://your-project-name.vercel.app`
   - Custom domain can be configured in Vercel dashboard

2. Test all routes:
   - `/` - Dashboard
   - `/clients` - Clients list
   - `/clients/:id` - Client detail
   - `/properties` - Properties list
   - `/schedule` - Schedule
   - `/documents` - Documents
   - `/reports` - Reports
   - `/settings` - Settings

## Troubleshooting

If you encounter build errors locally:
```bash
# Fix dist directory permissions if needed
sudo chown -R $(whoami):$(whoami) dist
rm -rf dist
npm run build
```

## Demo Credentials
- Email: <EMAIL>
- Password: demo123

## Features Included
- Mock authentication system
- 42 real client profiles
- 100 MLS property listings with photos
- Responsive design
- Persistent sidebar navigation
- Client dossier with AI matching placeholder