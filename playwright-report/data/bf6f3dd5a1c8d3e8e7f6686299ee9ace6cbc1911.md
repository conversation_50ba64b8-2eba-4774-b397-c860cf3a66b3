# Page snapshot

```yaml
- region "Notifications (F8)":
  - list
- region "Notifications alt+T"
- complementary:
  - link "Narissa Realty CRM":
    - /url: /
    - img
    - heading "Narissa" [level=1]
    - paragraph: Realty CRM
  - img
  - paragraph: Sign in to access your CRM
  - button "Login as Demo User" [disabled]:
    - img
    - text: Login as Demo User
  - heading "Quick Actions" [level=3]
  - link "Add Property":
    - /url: /properties
    - button "Add Property":
      - img
      - text: Add Property
  - link "Find Leads":
    - /url: /properties/search
    - button "Find Leads":
      - img
      - text: Find Leads
  - link "Send Email":
    - /url: /documents
    - button "Send Email":
      - img
      - text: Send Email
  - navigation:
    - link "Home":
      - /url: /
      - button "Home":
        - img
        - text: Home
    - link "Dashboard":
      - /url: /dashboard
      - button "Dashboard":
        - img
        - text: Dashboard
    - link "Clients 18":
      - /url: /clients
      - button "Clients 18":
        - img
        - text: Clients 18
    - link "Properties 8":
      - /url: /properties
      - button "Properties 8":
        - img
        - text: Properties 8
    - link "Documents 3":
      - /url: /documents
      - button "Documents 3":
        - img
        - text: Documents 3
    - link "Schedule":
      - /url: /schedule
      - button "Schedule":
        - img
        - text: Schedule
    - link "Reports":
      - /url: /reports
      - button "Reports":
        - img
        - text: Reports
    - link "Settings":
      - /url: /settings
      - button "Settings":
        - img
        - text: Settings
    - link "Dev Log":
      - /url: /dev-log
      - button "Dev Log":
        - img
        - text: Dev Log
  - link "Settings":
    - /url: /settings
    - button "Settings":
      - img
      - text: Settings
  - link "Help":
    - /url: /ai-assist
    - button "Help":
      - img
      - text: Help
- banner:
  - button "Search... ⌘ K":
    - img
    - text: Search... ⌘ K
  - button "3":
    - img
    - text: "3"
  - button "Sarah Johnson Real Estate Agent":
    - img
    - text: Sarah Johnson Real Estate Agent
- main:
  - heading "Dashboard" [level=2]
  - paragraph: Real-time insights into your real estate business
  - heading "🏥 System Health Check Healthy" [level=3]
  - strong: "Container Validation:"
  - text: Access this health check at
  - code: localhost:5002
  - text: or run
  - code: window.healthCheck.refresh()
  - text: in browser console.
  - heading "Active Clients" [level=3]
  - img
  - text: "13"
  - paragraph: 30 total clients
  - heading "Active Listings" [level=3]
  - img
  - text: "95"
  - paragraph: 0 pending, 0 sold
  - heading "Total Portfolio Value" [level=3]
  - img
  - text: $83,073,707
  - paragraph: Across all active listings
  - heading "Avg Property Price" [level=3]
  - img
  - text: $874,460
  - paragraph: Current market average
  - heading "Properties by Price Range" [level=3]
  - paragraph: Distribution of current listings
  - img: Under $500K $1M - $2M $2M - $5M Over $5M 0 15 30 45 60
  - heading "Client Budget Distribution" [level=3]
  - paragraph: Understanding your client base
  - img
  - text: "Under $500K: 1 $500K - $1M: 14 $1M - $2M: 10 $2M - $5M: 5 Over $5M: 0"
  - heading "Listings by City" [level=3]
  - paragraph: Geographic distribution of your properties
  - img: 0 0.25 0.5 0.75 1 Grass Valley Nevada City North San Juan Rough andReady
  - heading "Recent Client Activity" [level=3]
  - paragraph: Latest updates from your clients
  - img
  - paragraph: Jason Smith
  - paragraph: "Budget: $2,251,400 • Single Family"
  - img
  - img
  - paragraph: James Smith
  - paragraph: "Budget: $1,069,083 • Multi-Family"
  - img
  - img
  - paragraph: Katherine Williams
  - paragraph: "Budget: $1,017,086 • Multi-Family"
  - img
  - img
  - paragraph: Brian Farley
  - paragraph: "Budget: $778,884 • Multi-Family"
  - img
  - img
  - paragraph: Michael Harris
  - paragraph: "Budget: $573,981 • Multi-Family"
  - img
  - heading "New Listings This Week" [level=3]
  - paragraph: Recently added properties
```