# Staging Container - Self-contained copy of our CRM
# This creates a production-ready snapshot with everything baked in

FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files first (for better caching)
COPY package*.json ./

# Install ALL dependencies inside the container
# This includes @google/genai and everything else
RUN npm ci --include=dev

# Copy the entire application
COPY . .

# Copy the environment file (with API keys)
# In production, you'd use secrets management instead
COPY .env.local .env.local

# Build the application for production
RUN npm run build

# Expose the internal port (Vite preview uses 4173 by default)
EXPOSE 4173

# Run the production preview server
# This serves the built files, not dev mode
CMD ["npm", "run", "preview", "--", "--host", "0.0.0.0", "--port", "4173"]