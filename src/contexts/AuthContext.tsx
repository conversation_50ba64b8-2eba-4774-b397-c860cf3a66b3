import React, { createContext, useContext, useState, ReactNode } from 'react'

// Demo user specification per PRD V1.1
const DEMO_USER = {
  id: 'demo-001',
  name: '<PERSON><PERSON>',
  role: 'Senior Real Estate Agent',
  email: '<EMAIL>',
  avatar: '/avatars/demo-user.jpg',
  phone: '(*************',
  license: 'CA-DRE-01234567',
  yearsExperience: 8,
  specialties: ['Luxury Properties', 'First-Time Buyers', 'Investment Properties'],
  metrics: {
    activeListings: 24,
    totalClients: 156,
    dealsThisMonth: 7
  }
}

// TypeScript interfaces for authentication system
interface DemoUser {
  id: string
  name: string
  role: string
  email: string
  avatar: string
  phone: string
  license: string
  yearsExperience: number
  specialties: string[]
  metrics?: {
    activeListings: number
    totalClients: number
    dealsThisMonth: number
  }
}

interface AuthContextType {
  user: DemoUser | null
  isAuthenticated: boolean
  login: () => void
  logout: () => void
  loading: boolean
}

// Create authentication context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Custom hook to use authentication context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Authentication provider component
interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<DemoUser | null>(null)
  const [loading, setLoading] = useState(false)

  const login = () => {
    setLoading(true)
    // Simulate brief loading state for realistic demo experience
    setTimeout(() => {
      setUser(DEMO_USER)
      setLoading(false)
    }, 800)
  }

  const logout = () => {
    setUser(null)
  }

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    login,
    logout,
    loading
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export default AuthContext