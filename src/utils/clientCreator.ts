import { realClientsData } from '@/data/realClientsData';

export interface ClientCreationData {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  clientType?: 'buyer' | 'seller' | 'investor' | 'both';
  location?: string;
  budget?: string;
  notes?: string;
}

export interface ClientCreationResult {
  success: boolean;
  message: string;
  clientId?: number;
  validationErrors?: string[];
}

/**
 * Extract client data from natural language message
 */
export function extractClientData(message: string): ClientCreationData | null {
  const lowerMessage = message.toLowerCase();
  
  // Check for creation intent
  const creationPatterns = [
    /create client:?\s*(.+)/i,
    /add client:?\s*(.+)/i,
    /new client:?\s*(.+)/i,
    /add new client:?\s*(.+)/i
  ];
  
  let clientInfo = '';
  for (const pattern of creationPatterns) {
    const match = message.match(pattern);
    if (match) {
      clientInfo = match[1].trim();
      break;
    }
  }
  
  if (!clientInfo) return null;
  
  const data: ClientCreationData = {
    firstName: '',
    lastName: ''
  };
  
  // Extract name (first pattern that looks like a name)
  const namePattern = /([A-Z][a-z]+)\s+([A-Z][a-z]+)/;
  const nameMatch = clientInfo.match(namePattern);
  if (nameMatch) {
    data.firstName = nameMatch[1];
    data.lastName = nameMatch[2];
  }
  
  // Extract email
  const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
  const emailMatch = clientInfo.match(emailPattern);
  if (emailMatch) {
    data.email = emailMatch[1];
  }
  
  // Extract phone number
  const phonePatterns = [
    /\((\d{3})\)\s*(\d{3})-(\d{4})/,  // (*************
    /(\d{3})-(\d{3})-(\d{4})/,        // ************
    /(\d{3})\.(\d{3})\.(\d{4})/,      // ************
    /(\d{10})/                        // 5551234567
  ];
  
  for (const pattern of phonePatterns) {
    const phoneMatch = clientInfo.match(pattern);
    if (phoneMatch) {
      if (pattern === phonePatterns[3]) {
        // Format 10-digit number
        const digits = phoneMatch[1];
        data.phone = `(${digits.slice(0,3)}) ${digits.slice(3,6)}-${digits.slice(6)}`;
      } else {
        data.phone = `(${phoneMatch[1]}) ${phoneMatch[2]}-${phoneMatch[3]}`;
      }
      break;
    }
  }
  
  // Extract client type
  if (lowerMessage.includes('buyer')) data.clientType = 'buyer';
  else if (lowerMessage.includes('seller')) data.clientType = 'seller';
  else if (lowerMessage.includes('investor')) data.clientType = 'investor';
  else if (lowerMessage.includes('both')) data.clientType = 'both';
  
  // Extract location
  const locationPatterns = [
    /(?:in|from|located in|lives in)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)/i,
    /([A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)\s+area/i,
    /looking in\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)/i
  ];
  
  for (const pattern of locationPatterns) {
    const locationMatch = clientInfo.match(pattern);
    if (locationMatch) {
      data.location = locationMatch[1];
      break;
    }
  }
  
  // Extract budget
  const budgetPatterns = [
    /budget:?\s*\$?(\d+(?:,\d{3})*(?:k|K)?)/i,
    /\$(\d+(?:,\d{3})*(?:k|K)?)\s*budget/i,
    /up to\s*\$?(\d+(?:,\d{3})*(?:k|K)?)/i
  ];
  
  for (const pattern of budgetPatterns) {
    const budgetMatch = clientInfo.match(pattern);
    if (budgetMatch) {
      data.budget = budgetMatch[1];
      break;
    }
  }
  
  // Extract additional notes
  const notePatterns = [
    /notes?:\s*(.+)/i,
    /interested in\s+(.+)/i,
    /looking for\s+(.+)/i
  ];
  
  for (const pattern of notePatterns) {
    const noteMatch = clientInfo.match(pattern);
    if (noteMatch) {
      data.notes = noteMatch[1];
      break;
    }
  }
  
  return data.firstName && data.lastName ? data : null;
}

/**
 * Validate client data
 */
export function validateClientData(data: ClientCreationData): string[] {
  const errors: string[] = [];
  
  // Required fields
  if (!data.firstName.trim()) {
    errors.push('First name is required');
  }
  
  if (!data.lastName.trim()) {
    errors.push('Last name is required');
  }
  
  // Email validation
  if (data.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      errors.push('Invalid email format');
    }
  }
  
  // Phone validation
  if (data.phone) {
    const phoneRegex = /^\(\d{3}\) \d{3}-\d{4}$/;
    if (!phoneRegex.test(data.phone)) {
      errors.push('Invalid phone format (should be (XXX) XXX-XXXX)');
    }
  }
  
  return errors;
}

/**
 * Check for duplicate clients
 */
export function checkForDuplicates(data: ClientCreationData): Array<{id: number; name: string; reason: string}> {
  const duplicates: Array<{id: number; name: string; reason: string}> = [];
  
  for (const client of realClientsData) {
    const fullName = `${client.first_name} ${client.last_name}`;
    
    // Check name match
    if (client.first_name.toLowerCase() === data.firstName.toLowerCase() &&
        client.last_name.toLowerCase() === data.lastName.toLowerCase()) {
      duplicates.push({
        id: client.id,
        name: fullName,
        reason: 'Same name'
      });
      continue;
    }
    
    // Check email match
    if (data.email && client.email && 
        client.email.toLowerCase() === data.email.toLowerCase()) {
      duplicates.push({
        id: client.id,
        name: fullName,
        reason: 'Same email'
      });
      continue;
    }
    
    // Check phone match
    if (data.phone && client.mobile_phone && 
        client.mobile_phone === data.phone) {
      duplicates.push({
        id: client.id,
        name: fullName,
        reason: 'Same phone'
      });
    }
  }
  
  return duplicates;
}

/**
 * Generate new unique client ID
 */
export function generateClientId(): number {
  const existingIds = realClientsData.map(c => c.id);
  const maxId = Math.max(...existingIds);
  return maxId + 1;
}

/**
 * Create new client record
 */
export function createClient(data: ClientCreationData): ClientCreationResult {
  // Validate data
  const validationErrors = validateClientData(data);
  if (validationErrors.length > 0) {
    return {
      success: false,
      message: 'Validation failed',
      validationErrors
    };
  }
  
  // Check for duplicates
  const duplicates = checkForDuplicates(data);
  if (duplicates.length > 0) {
    const duplicateList = duplicates.map(d => `${d.name} (${d.reason})`).join(', ');
    return {
      success: false,
      message: `Potential duplicates found: ${duplicateList}. Please verify this is a new client.`
    };
  }
  
  // Create new client
  const newClientId = generateClientId();
  const newClient = {
    id: newClientId,
    first_name: data.firstName,
    last_name: data.lastName,
    email: data.email || null,
    mobile_phone: data.phone || null,
    city: data.location || null,
    client_type: data.clientType || null,
    status: 'prospect',
    stage: 'lead',
    priority: 'normal',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    notes: data.notes || null,
    // Parse budget if provided
    price_range_max: data.budget ? parseBudget(data.budget).toString() : null,
    pre_approval_amount: data.budget ? parseBudget(data.budget).toString() : null
  };
  
  // Add to in-memory data (in a real app, this would be a database call)
  realClientsData.push(newClient as any);
  
  return {
    success: true,
    message: `✅ Client created successfully! [[View ${data.firstName} ${data.lastName}'s profile|client:${data.firstName} ${data.lastName}]]`,
    clientId: newClientId
  };
}

/**
 * Parse budget string to number
 */
function parseBudget(budgetStr: string): number {
  const cleanBudget = budgetStr.replace(/[,$]/g, '');
  
  if (cleanBudget.toLowerCase().includes('k')) {
    return parseFloat(cleanBudget.replace(/k/i, '')) * 1000;
  }
  
  return parseFloat(cleanBudget);
}

/**
 * Generate client creation confirmation
 */
export function generateClientCreationConfirmation(data: ClientCreationData): string {
  const summary = [
    `**Name:** ${data.firstName} ${data.lastName}`,
    data.email ? `**Email:** ${data.email}` : null,
    data.phone ? `**Phone:** ${data.phone}` : null,
    data.clientType ? `**Type:** ${data.clientType}` : null,
    data.location ? `**Location:** ${data.location}` : null,
    data.budget ? `**Budget:** $${data.budget}` : null,
    data.notes ? `**Notes:** ${data.notes}` : null
  ].filter(Boolean).join('\n');
  
  return `I'll create a new client with this information:\n\n${summary}\n\nReply "yes" to confirm or "no" to cancel.`;
}
