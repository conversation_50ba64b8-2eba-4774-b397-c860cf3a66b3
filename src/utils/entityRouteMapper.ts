import { realClientsData } from '@/data/realClientsData';
import { realPropertiesData } from '@/data/realPropertiesData';

export interface EntityLink {
  text: string;
  route: string;
  type: 'client' | 'property' | 'schedule' | 'page';
}

/**
 * Enhanced entity detection and link generation
 */
export function detectAndGenerateLinks(message: string): { message: string; entities: Array<{type: string; value: string; route: string}> } {
  const entities: Array<{type: string; value: string; route: string}> = [];
  let processedMessage = message;

  // Detect client names (look for patterns like "<PERSON>'s profile", "<PERSON> Johnson", etc.)
  const clientPatterns = [
    /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)'s?\s+(?:profile|client|info|details|record)\b/gi,
    /\b(?:client|contact)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)\b/gi,
    /\b([A-Z][a-z]+\s+[A-Z][a-z]+)\s+(?:is|was|has|needs|wants)\b/gi
  ];

  clientPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(message)) !== null) {
      const clientName = match[1].trim();
      const route = findClientRoute(clientName);
      if (route && !entities.find(e => e.value === clientName)) {
        entities.push({ type: 'client', value: clientName, route });
      }
    }
  });

  // Detect property addresses
  const propertyPatterns = [
    /\b(\d+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:\s+(?:St|Street|Ave|Avenue|Rd|Road|Dr|Drive|Ln|Lane|Ct|Court|Pl|Place|Way|Trail|Blvd|Boulevard))?)\b/gi,
    /\b(?:property|house|home)\s+(?:at|on)\s+([^,.\n]+)/gi
  ];

  propertyPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(message)) !== null) {
      const address = match[1].trim();
      const route = findPropertyRoute(address);
      if (route && !entities.find(e => e.value === address)) {
        entities.push({ type: 'property', value: address, route });
      }
    }
  });

  // Detect schedule/appointment references
  const schedulePatterns = [
    /\b(?:schedule|book|set up)\s+(?:a\s+)?(?:meeting|appointment|showing)\b/gi,
    /\b(?:calendar|schedule)\b/gi
  ];

  schedulePatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(message)) !== null) {
      const scheduleContext = match[0];
      const route = findScheduleRoute(scheduleContext);
      if (route && !entities.find(e => e.type === 'schedule')) {
        entities.push({ type: 'schedule', value: 'Schedule', route });
      }
    }
  });

  return { message: processedMessage, entities };
}

/**
 * Find a client by name with enhanced matching capabilities
 */
export function findClientRoute(name: string): string | null {
  const searchName = name.toLowerCase().trim();

  // Try exact full name match first
  let client = realClientsData.find(c => {
    const fullName = `${c.first_name} ${c.last_name}`.toLowerCase();
    return fullName === searchName;
  });

  if (client) {
    return `/clients/${client.id}`;
  }

  // Try first name + last name partial match
  client = realClientsData.find(c => {
    const firstName = c.first_name.toLowerCase();
    const lastName = c.last_name.toLowerCase();
    const fullName = `${firstName} ${lastName}`;

    // Check if search contains both first and last name
    return searchName.includes(firstName) && searchName.includes(lastName);
  });

  if (client) {
    return `/clients/${client.id}`;
  }

  // Try first name only match (if unique)
  const firstNameMatches = realClientsData.filter(c =>
    c.first_name.toLowerCase() === searchName
  );

  if (firstNameMatches.length === 1) {
    return `/clients/${firstNameMatches[0].id}`;
  }

  // Try last name only match (if unique)
  const lastNameMatches = realClientsData.filter(c =>
    c.last_name.toLowerCase() === searchName
  );

  if (lastNameMatches.length === 1) {
    return `/clients/${lastNameMatches[0].id}`;
  }

  // No unique match found
  return null;
}

/**
 * Find a property by address with enhanced matching capabilities
 */
export function findPropertyRoute(address: string): string | null {
  const searchAddress = address.toLowerCase().trim();

  // Try exact address match first
  let property = realPropertiesData.find(p => {
    if (!p.address_street_complete) return false;
    const fullAddress = p.address_street_complete.toLowerCase();
    return fullAddress === searchAddress;
  });

  if (property) {
    return `/properties/${property.id}`;
  }

  // Try partial address match
  property = realPropertiesData.find(p => {
    if (!p.address_street_complete) return false;
    const fullAddress = p.address_street_complete.toLowerCase();
    const streetAddress = fullAddress.split(',')[0];

    return fullAddress.includes(searchAddress) ||
           streetAddress.includes(searchAddress);
  });

  if (property) {
    return `/properties/${property.id}`;
  }

  // Try matching by street number and name (e.g., "123 Main" matches "123 Main St")
  const addressParts = searchAddress.split(' ');
  if (addressParts.length >= 2) {
    const streetNumber = addressParts[0];
    const streetName = addressParts.slice(1).join(' ');

    property = realPropertiesData.find(p => {
      if (!p.address_street_complete) return false;
      const fullAddress = p.address_street_complete.toLowerCase();
      return fullAddress.includes(streetNumber) && fullAddress.includes(streetName);
    });

    if (property) {
      return `/properties/${property.id}`;
    }
  }

  return null;
}

/**
 * Find appointment/schedule route based on context
 */
export function findScheduleRoute(context: string): string | null {
  const lowerContext = context.toLowerCase().trim();

  // For now, all schedule-related queries go to the main schedule page
  // In the future, this could route to specific appointments or time slots
  if (lowerContext.includes('appointment') ||
      lowerContext.includes('meeting') ||
      lowerContext.includes('schedule') ||
      lowerContext.includes('calendar')) {
    return '/schedule';
  }

  return null;
}

/**
 * Parse a message and extract entity links in format [[text|type:identifier]]
 * Examples:
 * - [[View Aaron's profile|client:Aaron Wilson]]
 * - [[123 Main St|property:123 Main St]]
 * - [[Schedule|page:schedule]]
 */
export function parseEntityLinks(message: string): { 
  parsedMessage: string; 
  links: EntityLink[] 
} {
  const links: EntityLink[] = [];
  const linkRegex = /\[\[([^|]+)\|([^:]+):([^\]]+)\]\]/g;
  
  const parsedMessage = message.replace(linkRegex, (match, text, type, identifier) => {
    let route: string | null = null;
    
    switch (type) {
      case 'client':
        route = findClientRoute(identifier);
        break;
      case 'property':
        route = findPropertyRoute(identifier);
        break;
      case 'schedule':
        route = findScheduleRoute(identifier);
        break;
      case 'page':
        // Direct page routes
        const pageRoutes: Record<string, string> = {
          'schedule': '/schedule',
          'dashboard': '/dashboard',
          'clients': '/clients',
          'properties': '/properties',
          'reports': '/reports',
          'settings': '/settings'
        };
        route = pageRoutes[identifier.toLowerCase()];
        break;
      default:
        route = null;
    }
    
    if (route) {
      links.push({ text, route, type: type as EntityLink['type'] });
      return `{{link:${links.length - 1}}}`; // Placeholder for link
    }
    
    return text; // If no route found, just return the text
  });
  
  return { parsedMessage, links };
}

/**
 * Convert AI response with markdown-style links to our entity link format
 * Converts [text](route) to [[text|type:identifier]]
 */
export function convertMarkdownLinks(message: string): string {
  // Handle direct routes like [View Profile](/clients/123)
  message = message.replace(/\[([^\]]+)\]\(\/clients\/(\d+)\)/g, (match, text, clientId) => {
    const client = realClientsData.find(c => c.id === clientId);
    if (client) {
      return `[[${text}|client:${client.first_name} ${client.last_name}]]`;
    }
    return match;
  });
  
  // Handle property routes
  message = message.replace(/\[([^\]]+)\]\(\/properties\/(\d+)\)/g, (match, text, propertyId) => {
    const property = realPropertiesData.find(p => p.id === propertyId);
    if (property) {
      return `[[${text}|property:${property.address}]]`;
    }
    return match;
  });
  
  // Handle page routes
  message = message.replace(/\[([^\]]+)\]\(\/(schedule|dashboard|clients|properties|reports|settings)\)/g, 
    (match, text, page) => `[[${text}|page:${page}]]`
  );
  
  return message;
}

/**
 * Auto-generate links in AI responses by detecting entities
 */
export function autoGenerateLinks(message: string): string {
  const { entities } = detectAndGenerateLinks(message);
  let processedMessage = message;

  // Replace detected entities with link syntax
  entities.forEach(entity => {
    const linkText = entity.type === 'client' ? `${entity.value}'s profile` :
                     entity.type === 'property' ? `${entity.value}` :
                     entity.type === 'schedule' ? 'Schedule' : entity.value;

    const linkSyntax = `[[${linkText}|${entity.type}:${entity.value}]]`;

    // Replace first occurrence of the entity value with link syntax
    const regex = new RegExp(`\\b${entity.value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
    processedMessage = processedMessage.replace(regex, linkSyntax);
  });

  return processedMessage;
}