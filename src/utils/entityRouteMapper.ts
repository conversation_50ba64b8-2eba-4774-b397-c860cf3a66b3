import { realClientsData } from '@/data/realClientsData';
import { realPropertiesData } from '@/data/realPropertiesData';

export interface EntityLink {
  text: string;
  route: string;
  type: 'client' | 'property' | 'schedule' | 'page';
}

/**
 * Find a client by name and return their route
 */
export function findClientRoute(name: string): string | null {
  const searchName = name.toLowerCase().trim();
  
  // STRICT matching only - no fuzzy matching
  const client = realClientsData.find(c => {
    const fullName = `${c.first_name} ${c.last_name}`.toLowerCase();
    
    // Only exact full name match
    return fullName === searchName;
  });
  
  // If no exact match found, return null - don't guess
  return client ? `/clients/${client.id}` : null;
}

/**
 * Find a property by address and return its route
 */
export function findPropertyRoute(address: string): string | null {
  const searchAddress = address.toLowerCase().trim();
  
  const property = realPropertiesData.find(p => {
    if (!p.address_street_complete) return false;
    const fullAddress = p.address_street_complete.toLowerCase();
    const streetAddress = fullAddress.split(',')[0];
    
    return fullAddress.includes(searchAddress) || 
           streetAddress.includes(searchAddress);
  });
  
  return property ? `/properties/${property.id}` : null;
}

/**
 * Parse a message and extract entity links in format [[text|type:identifier]]
 * Examples:
 * - [[View Aaron's profile|client:Aaron Wilson]]
 * - [[123 Main St|property:123 Main St]]
 * - [[Schedule|page:schedule]]
 */
export function parseEntityLinks(message: string): { 
  parsedMessage: string; 
  links: EntityLink[] 
} {
  const links: EntityLink[] = [];
  const linkRegex = /\[\[([^|]+)\|([^:]+):([^\]]+)\]\]/g;
  
  const parsedMessage = message.replace(linkRegex, (match, text, type, identifier) => {
    let route: string | null = null;
    
    switch (type) {
      case 'client':
        route = findClientRoute(identifier);
        break;
      case 'property':
        route = findPropertyRoute(identifier);
        break;
      case 'page':
        // Direct page routes
        const pageRoutes: Record<string, string> = {
          'schedule': '/schedule',
          'dashboard': '/dashboard',
          'clients': '/clients',
          'properties': '/properties',
          'reports': '/reports',
          'settings': '/settings'
        };
        route = pageRoutes[identifier.toLowerCase()];
        break;
      default:
        route = null;
    }
    
    if (route) {
      links.push({ text, route, type: type as EntityLink['type'] });
      return `{{link:${links.length - 1}}}`; // Placeholder for link
    }
    
    return text; // If no route found, just return the text
  });
  
  return { parsedMessage, links };
}

/**
 * Convert AI response with markdown-style links to our entity link format
 * Converts [text](route) to [[text|type:identifier]]
 */
export function convertMarkdownLinks(message: string): string {
  // Handle direct routes like [View Profile](/clients/123)
  message = message.replace(/\[([^\]]+)\]\(\/clients\/(\d+)\)/g, (match, text, clientId) => {
    const client = realClientsData.find(c => c.id === clientId);
    if (client) {
      return `[[${text}|client:${client.first_name} ${client.last_name}]]`;
    }
    return match;
  });
  
  // Handle property routes
  message = message.replace(/\[([^\]]+)\]\(\/properties\/(\d+)\)/g, (match, text, propertyId) => {
    const property = realPropertiesData.find(p => p.id === propertyId);
    if (property) {
      return `[[${text}|property:${property.address}]]`;
    }
    return match;
  });
  
  // Handle page routes
  message = message.replace(/\[([^\]]+)\]\(\/(schedule|dashboard|clients|properties|reports|settings)\)/g, 
    (match, text, page) => `[[${text}|page:${page}]]`
  );
  
  return message;
}