import { realClientsData } from '@/data/realClientsData';
import { realPropertiesData } from '@/data/realPropertiesData';

/**
 * Validates AI response to ensure all mentioned client names actually exist in the database
 * This prevents hallucination by checking every name against our data
 */
export function validateAIResponse(response: string, searchContext?: { clientNames?: string[], propertyAddresses?: string[] }): {
  isValid: boolean;
  issues: string[];
  correctedResponse?: string;
} {
  const issues: string[] = [];
  let correctedResponse = response;
  
  // Build a set of valid client names from search context and database
  const validClientNames = new Set<string>();
  
  // Add all real client names in lowercase for comparison
  realClientsData.forEach(client => {
    const fullName = `${client.first_name} ${client.last_name}`.toLowerCase();
    validClientNames.add(fullName);
  });
  
  // Also add names from search context if provided
  if (searchContext?.clientNames) {
    searchContext.clientNames.forEach(name => validClientNames.add(name.toLowerCase()));
  }
  
  // Extract all potential client names from the response
  // Look for patterns like "FirstName LastName" or names in links [[...|client:Name]]
  const namePatterns = [
    // Regular name pattern: Capital Letter followed by capital letter (First Last)
    /\b([A-Z][a-z]+)\s+([A-Z][a-z]+)\b/g,
    // Names in links
    /\[\[.*?\|client:([^]]+)\]\]/g
  ];
  
  const mentionedNames = new Set<string>();
  
  namePatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(response)) !== null) {
      if (pattern === namePatterns[0]) {
        // Regular name pattern
        const fullName = `${match[1]} ${match[2]}`;
        mentionedNames.add(fullName);
      } else {
        // Link pattern
        mentionedNames.add(match[1]);
      }
    }
  });
  
  // Check each mentioned name
  mentionedNames.forEach(name => {
    const nameLower = name.toLowerCase();
    
    // Skip common words that might look like names
    const commonWords = ['the property', 'the client', 'real estate', 'client type'];
    if (commonWords.some(word => nameLower.includes(word))) {
      return;
    }
    
    // Check if this name exists in our valid set
    if (!validClientNames.has(nameLower)) {
      issues.push(`Mentioned non-existent client: "${name}"`);
      
      // Try to find close matches
      const closeMatches: string[] = [];
      realClientsData.forEach(client => {
        const fullName = `${client.first_name} ${client.last_name}`;
        const fullNameLower = fullName.toLowerCase();
        
        // Check if last name matches
        if (client.last_name.toLowerCase() === name.split(' ')[1]?.toLowerCase()) {
          closeMatches.push(fullName);
        }
      });
      
      // Replace the hallucinated name in the response
      if (closeMatches.length > 0) {
        correctedResponse = correctedResponse.replace(
          new RegExp(`\\b${name}\\b`, 'g'),
          `[Name not found - did you mean ${closeMatches[0]}?]`
        );
      } else {
        correctedResponse = correctedResponse.replace(
          new RegExp(`\\b${name}\\b`, 'g'),
          `[Name "${name}" not found in database]`
        );
      }
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues,
    correctedResponse: issues.length > 0 ? correctedResponse : undefined
  };
}

/**
 * Extract valid client names from search results for validation context
 */
export function extractNamesFromSearchResults(searchResults: string): string[] {
  const names: string[] = [];
  
  // Pattern to match names in search results format: "- FirstName LastName (type, location)"
  const pattern = /^- ([A-Z][a-z]+\s+[A-Z][a-z]+)\s*\(/gm;
  let match;
  
  while ((match = pattern.exec(searchResults)) !== null) {
    names.push(match[1]);
  }
  
  return names;
}