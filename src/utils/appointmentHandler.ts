export interface Appointment {
  id: string;
  title: string;
  type: "showing" | "meeting" | "inspection" | "closing" | "other";
  clientId?: number;
  propertyId?: string;
  date: Date;
  time: string;
  duration: number; // in minutes
  location: string;
  notes: string;
  status: "scheduled" | "completed" | "cancelled";
  createdAt: Date;
  updatedAt: Date;
}

export interface AppointmentCreationData {
  title: string;
  type: "showing" | "meeting" | "inspection" | "closing" | "other";
  clientId?: number;
  propertyId?: string;
  date: Date;
  time: string;
  duration: number;
  location: string;
  notes: string;
}

export interface AppointmentCreationResult {
  success: boolean;
  message: string;
  appointmentId?: string;
  validationErrors?: string[];
}

/**
 * Generate unique appointment ID
 */
export function generateAppointmentId(): string {
  return `apt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validate appointment data
 */
export function validateAppointmentData(data: AppointmentCreationData): string[] {
  const errors: string[] = [];
  
  // Required fields
  if (!data.title.trim()) {
    errors.push('Title is required');
  }
  
  if (!data.type) {
    errors.push('Appointment type is required');
  }
  
  if (!data.date) {
    errors.push('Date is required');
  }
  
  if (!data.time.trim()) {
    errors.push('Time is required');
  }
  
  if (!data.location.trim()) {
    errors.push('Location is required');
  }
  
  if (data.duration <= 0) {
    errors.push('Duration must be greater than 0');
  }
  
  // Date validation
  if (data.date && data.date < new Date(new Date().setHours(0, 0, 0, 0))) {
    errors.push('Cannot schedule appointments in the past');
  }
  
  // Time format validation (basic)
  const timeRegex = /^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i;
  if (data.time && !timeRegex.test(data.time)) {
    errors.push('Time must be in format "HH:MM AM/PM" (e.g., "2:30 PM")');
  }
  
  return errors;
}

/**
 * Save appointment to localStorage
 */
export function saveAppointment(data: AppointmentCreationData): AppointmentCreationResult {
  try {
    // Validate data
    const validationErrors = validateAppointmentData(data);
    if (validationErrors.length > 0) {
      return {
        success: false,
        message: 'Validation failed',
        validationErrors
      };
    }
    
    // Create appointment object
    const appointmentId = generateAppointmentId();
    const appointment: Appointment = {
      id: appointmentId,
      title: data.title,
      type: data.type,
      clientId: data.clientId,
      propertyId: data.propertyId,
      date: data.date,
      time: data.time,
      duration: data.duration,
      location: data.location,
      notes: data.notes,
      status: 'scheduled',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Get existing appointments
    const existingAppointments = getAllAppointments();
    
    // Add new appointment
    const updatedAppointments = [...existingAppointments, appointment];
    
    // Save to localStorage
    localStorage.setItem('appointments', JSON.stringify(updatedAppointments));
    
    return {
      success: true,
      message: `✅ Appointment "${data.title}" scheduled successfully!`,
      appointmentId
    };
  } catch (error) {
    console.error('Error saving appointment:', error);
    return {
      success: false,
      message: `Failed to save appointment: ${error}`
    };
  }
}

/**
 * Get all appointments from localStorage
 */
export function getAllAppointments(): Appointment[] {
  try {
    const appointmentsJson = localStorage.getItem('appointments');
    if (!appointmentsJson) return [];
    
    const appointments = JSON.parse(appointmentsJson);
    return appointments.map((apt: any) => ({
      ...apt,
      date: new Date(apt.date),
      createdAt: new Date(apt.createdAt),
      updatedAt: new Date(apt.updatedAt)
    }));
  } catch (error) {
    console.error('Error loading appointments:', error);
    return [];
  }
}

/**
 * Update appointment status
 */
export function updateAppointmentStatus(appointmentId: string, status: Appointment['status']): boolean {
  try {
    const appointments = getAllAppointments();
    const appointmentIndex = appointments.findIndex(apt => apt.id === appointmentId);
    
    if (appointmentIndex === -1) {
      return false;
    }
    
    appointments[appointmentIndex].status = status;
    appointments[appointmentIndex].updatedAt = new Date();
    
    localStorage.setItem('appointments', JSON.stringify(appointments));
    return true;
  } catch (error) {
    console.error('Error updating appointment status:', error);
    return false;
  }
}

/**
 * Delete appointment
 */
export function deleteAppointment(appointmentId: string): boolean {
  try {
    const appointments = getAllAppointments();
    const filteredAppointments = appointments.filter(apt => apt.id !== appointmentId);
    
    localStorage.setItem('appointments', JSON.stringify(filteredAppointments));
    return true;
  } catch (error) {
    console.error('Error deleting appointment:', error);
    return false;
  }
}

/**
 * Get appointments for a specific date
 */
export function getAppointmentsForDate(date: Date): Appointment[] {
  const appointments = getAllAppointments();
  return appointments.filter(apt => {
    const aptDate = new Date(apt.date);
    return aptDate.toDateString() === date.toDateString();
  });
}

/**
 * Check for appointment conflicts
 */
export function checkAppointmentConflicts(date: Date, time: string, duration: number, excludeId?: string): Appointment[] {
  const appointments = getAppointmentsForDate(date);
  const conflicts: Appointment[] = [];
  
  // Convert time to minutes for easier comparison
  const [timeStr, period] = time.split(' ');
  const [hours, minutes] = timeStr.split(':').map(Number);
  const startMinutes = (period.toUpperCase() === 'PM' && hours !== 12 ? hours + 12 : hours) * 60 + minutes;
  const endMinutes = startMinutes + duration;
  
  for (const apt of appointments) {
    if (excludeId && apt.id === excludeId) continue;
    if (apt.status === 'cancelled') continue;
    
    const [aptTimeStr, aptPeriod] = apt.time.split(' ');
    const [aptHours, aptMinutes] = aptTimeStr.split(':').map(Number);
    const aptStartMinutes = (aptPeriod.toUpperCase() === 'PM' && aptHours !== 12 ? aptHours + 12 : aptHours) * 60 + aptMinutes;
    const aptEndMinutes = aptStartMinutes + apt.duration;
    
    // Check for overlap
    if ((startMinutes < aptEndMinutes) && (endMinutes > aptStartMinutes)) {
      conflicts.push(apt);
    }
  }
  
  return conflicts;
}

/**
 * Parse appointment creation from natural language (for AI integration)
 */
export function parseAppointmentCreation(message: string): AppointmentCreationData | null {
  const lowerMessage = message.toLowerCase();
  
  // Check for creation intent
  const creationPatterns = [
    /schedule\s+(?:a\s+)?(?:meeting|appointment|showing)\s+(.+)/i,
    /book\s+(?:a\s+)?(?:meeting|appointment|showing)\s+(.+)/i,
    /add\s+(?:a\s+)?(?:meeting|appointment|showing)\s+(.+)/i,
    /create\s+(?:a\s+)?(?:meeting|appointment|showing)\s+(.+)/i
  ];
  
  let appointmentInfo = '';
  for (const pattern of creationPatterns) {
    const match = message.match(pattern);
    if (match) {
      appointmentInfo = match[1].trim();
      break;
    }
  }
  
  if (!appointmentInfo) return null;
  
  // Basic parsing - this could be enhanced significantly
  const data: AppointmentCreationData = {
    title: appointmentInfo,
    type: 'meeting', // default
    date: new Date(), // default to today
    time: '10:00 AM', // default
    duration: 60, // default 1 hour
    location: 'Office',
    notes: ''
  };
  
  // Try to extract type
  if (lowerMessage.includes('showing')) data.type = 'showing';
  else if (lowerMessage.includes('inspection')) data.type = 'inspection';
  else if (lowerMessage.includes('closing')) data.type = 'closing';
  else if (lowerMessage.includes('meeting')) data.type = 'meeting';
  
  return data;
}
