// Simple event system for data changes in demo environment
// This allows UI components to react to AI-driven data modifications

type DataChangeListener = () => void;

class DataEventEmitter {
  private listeners: Set<DataChangeListener> = new Set();

  addListener(listener: DataChangeListener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  emit() {
    this.listeners.forEach(listener => listener());
  }
}

export const clientDataEvents = new DataEventEmitter();
export const noteDataEvents = new DataEventEmitter();
export const appointmentDataEvents = new DataEventEmitter();