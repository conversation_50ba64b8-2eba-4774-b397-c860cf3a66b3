import { AIMessage } from '@/services/aiService';

const STORAGE_KEY = 'ai_chat_messages';
const MAX_MESSAGES = 30; // Keep last 30 messages

export const chatStorage = {
  /**
   * Save messages to sessionStorage
   */
  saveMessages(messages: AIMessage[]) {
    try {
      // Keep only the last MAX_MESSAGES
      const messagesToSave = messages.slice(-MAX_MESSAGES);
      sessionStorage.setItem(STORAGE_KEY, JSON.stringify(messagesToSave));
    } catch (error) {
      console.error('Failed to save chat messages:', error);
    }
  },

  /**
   * Load messages from sessionStorage
   */
  loadMessages(): AIMessage[] {
    try {
      const stored = sessionStorage.getItem(STORAGE_KEY);
      if (!stored) return [];
      
      const messages = JSON.parse(stored);
      // Convert timestamp strings back to Date objects
      return messages.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }));
    } catch (error) {
      console.error('Failed to load chat messages:', error);
      return [];
    }
  },

  /**
   * Clear all stored messages
   */
  clearMessages() {
    try {
      sessionStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear chat messages:', error);
    }
  }
};