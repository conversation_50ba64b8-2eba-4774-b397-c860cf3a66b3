import { realClientsData } from '@/data/realClientsData';
import { noteDataEvents } from '@/utils/dataEvents';
import { findClientRoute } from './entityRouteMapper';

export interface NoteAddIntent {
  clientName: string;
  noteContent: string;
  confidence: number;
  clientId?: number;
  ambiguousMatches?: Array<{id: number; name: string}>;
}

export interface ClientNote {
  id: string;
  content: string;
  timestamp: Date;
  addedBy: string;
}

/**
 * Parse user message to detect note-adding intent
 */
export function parseNoteAddIntent(message: string): NoteAddIntent | null {
  const lowerMessage = message.toLowerCase();
  
  // Intent detection patterns
  const notePatterns = [
    /add note to ([^:]+):\s*(.+)/i,
    /note for ([^:]+):\s*(.+)/i,
    /add to ([^']+)'s notes?:\s*(.+)/i,
    /note ([^:]+):\s*(.+)/i,
    /add note about ([^:]+):\s*(.+)/i,
    /make a note that ([^:]+)\s+(.+)/i
  ];
  
  for (const pattern of notePatterns) {
    const match = message.match(pattern);
    if (match) {
      const clientName = match[1].trim();
      const noteContent = match[2].trim();
      
      // Find matching clients
      const exactMatch = realClientsData.find(c => 
        `${c.first_name} ${c.last_name}`.toLowerCase() === clientName.toLowerCase()
      );
      
      if (exactMatch) {
        return {
          clientName: `${exactMatch.first_name} ${exactMatch.last_name}`,
          noteContent,
          confidence: 0.95,
          clientId: exactMatch.id
        };
      }
      
      // Find partial matches
      const partialMatches = realClientsData.filter(c => {
        const fullName = `${c.first_name} ${c.last_name}`.toLowerCase();
        const firstName = c.first_name.toLowerCase();
        const lastName = c.last_name.toLowerCase();
        const searchName = clientName.toLowerCase();
        
        return fullName.includes(searchName) || 
               firstName.includes(searchName) || 
               lastName.includes(searchName);
      });
      
      if (partialMatches.length === 1) {
        return {
          clientName: `${partialMatches[0].first_name} ${partialMatches[0].last_name}`,
          noteContent,
          confidence: 0.8,
          clientId: partialMatches[0].id
        };
      }
      
      if (partialMatches.length > 1) {
        return {
          clientName,
          noteContent,
          confidence: 0.6,
          ambiguousMatches: partialMatches.map(c => ({
            id: c.id,
            name: `${c.first_name} ${c.last_name}`
          }))
        };
      }
      
      // No matches found
      return {
        clientName,
        noteContent,
        confidence: 0.3
      };
    }
  }
  
  return null;
}

/**
 * Save note to localStorage
 */
export function saveClientNote(clientId: number, noteContent: string): boolean {
  try {
    const storageKey = `client_notes_${clientId}`;
    const existingNotes = getClientNotes(clientId);
    
    const newNote: ClientNote = {
      id: `note_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content: noteContent,
      timestamp: new Date(),
      addedBy: 'AI Assistant'
    };
    
    const updatedNotes = [...existingNotes, newNote];
    localStorage.setItem(storageKey, JSON.stringify(updatedNotes));
    
    // Notify UI components that note data has changed
    noteDataEvents.emit();
    
    return true;
  } catch (error) {
    console.error('Error saving client note:', error);
    return false;
  }
}

/**
 * Get all notes for a client
 */
export function getClientNotes(clientId: number): ClientNote[] {
  try {
    const storageKey = `client_notes_${clientId}`;
    const notesJson = localStorage.getItem(storageKey);
    
    if (!notesJson) return [];
    
    const notes = JSON.parse(notesJson);
    return notes.map((note: any) => ({
      ...note,
      timestamp: new Date(note.timestamp)
    }));
  } catch (error) {
    console.error('Error loading client notes:', error);
    return [];
  }
}

/**
 * Generate confirmation message for note addition
 */
export function generateNoteConfirmation(intent: NoteAddIntent): string {
  if (intent.confidence >= 0.8 && intent.clientId) {
    const client = realClientsData.find(c => c.id === intent.clientId);
    if (client) {
      return `Should I add this note to [[${client.first_name} ${client.last_name}'s profile|client:${client.first_name} ${client.last_name}]]?\n\n**Note:** "${intent.noteContent}"\n\nReply "yes" to confirm or "no" to cancel.`;
    }
  }
  
  if (intent.ambiguousMatches && intent.ambiguousMatches.length > 0) {
    const matchList = intent.ambiguousMatches
      .map((match, index) => `${index + 1}. [[${match.name}|client:${match.name}]]`)
      .join('\n');
    
    return `I found multiple clients matching "${intent.clientName}". Which one did you mean?\n\n${matchList}\n\n**Note to add:** "${intent.noteContent}"\n\nPlease specify the number or full name.`;
  }
  
  return `I couldn't find a client named "${intent.clientName}". Please check the spelling or use the full name.\n\n**Note content:** "${intent.noteContent}"`;
}

/**
 * Process note confirmation response
 */
export function processNoteConfirmation(response: string, intent: NoteAddIntent): { success: boolean; message: string } {
  const lowerResponse = response.toLowerCase().trim();
  
  if (lowerResponse === 'yes' && intent.clientId) {
    const success = saveClientNote(intent.clientId, intent.noteContent);
    
    if (success) {
      const client = realClientsData.find(c => c.id === intent.clientId);
      return {
        success: true,
        message: `✅ Note added to [[${client?.first_name} ${client?.last_name}'s profile|client:${client?.first_name} ${client?.last_name}]]!\n\n**Added:** "${intent.noteContent}"`
      };
    } else {
      return {
        success: false,
        message: '❌ Failed to save note. Please try again.'
      };
    }
  }
  
  if (lowerResponse === 'no') {
    return {
      success: false,
      message: 'Note addition cancelled.'
    };
  }
  
  // Handle numbered selection for ambiguous matches
  const numberMatch = response.match(/^(\d+)$/);
  if (numberMatch && intent.ambiguousMatches) {
    const index = parseInt(numberMatch[1]) - 1;
    if (index >= 0 && index < intent.ambiguousMatches.length) {
      const selectedClient = intent.ambiguousMatches[index];
      const success = saveClientNote(selectedClient.id, intent.noteContent);
      
      if (success) {
        return {
          success: true,
          message: `✅ Note added to [[${selectedClient.name}'s profile|client:${selectedClient.name}]]!\n\n**Added:** "${intent.noteContent}"`
        };
      } else {
        return {
          success: false,
          message: '❌ Failed to save note. Please try again.'
        };
      }
    }
  }
  
  return {
    success: false,
    message: 'Please respond with "yes", "no", or select a number from the list.'
  };
}

/**
 * Check if message is a note confirmation response
 */
export function isNoteConfirmationResponse(message: string): boolean {
  const lowerMessage = message.toLowerCase().trim();
  return lowerMessage === 'yes' || 
         lowerMessage === 'no' || 
         /^\d+$/.test(message.trim());
}
