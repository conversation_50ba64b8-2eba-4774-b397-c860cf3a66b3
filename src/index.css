
@tailwind base;
@tailwind components;
@tailwind utilities;

/* OpenAI Liquid Glass Theme (Blue & White) */

@layer base {
  :root {
    --background: 210 40% 98%; /* #F8FAFC */
    --foreground: 225 16% 16%; /* #232941 */

    --card: 0 0% 100%; /* #FFF */
    --card-foreground: 225 16% 16%;

    --popover: 0 0% 100%;
    --popover-foreground: 225 16% 16%;

    --primary: 214 73% 46%; /* #2563EB, OpenAI-inspired blue */
    --primary-foreground: 210 40% 98%; /* #F8FAFC */

    --secondary: 210 40% 96%; /* #F1F5F9 */
    --secondary-foreground: 225 16% 16%;

    --muted: 210 40% 94%; /* #E2E8F0 */
    --muted-foreground: 220 9% 46%;

    --accent: 214 73% 95%; /* Pale blue glass */
    --accent-foreground: 225 16% 16%;

    --destructive: 0 72% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 210 36% 92%; /* #E0E7EF */
    --input: 210 36% 92%;
    --ring: 214 73% 46%;

    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
}

/* Glassmorphism Utilities */
@layer utilities {
  .glass {
    @apply bg-white/20 backdrop-blur-xl border border-white/30 shadow-lg;
  }
  
  .glass-strong {
    @apply bg-white/30 backdrop-blur-xl border border-white/40 shadow-xl;
  }
  
  .glass-subtle {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 shadow-md;
  }
  
  .glass-cyan {
    @apply bg-gradient-to-br from-cyan-100/30 to-cyan-200/20 backdrop-blur-xl border border-cyan-200/40;
  }
  
  .glass-sienna {
    @apply bg-gradient-to-br from-orange-100/30 to-orange-200/20 backdrop-blur-xl border border-orange-200/40;
  }
  
  .agent-card {
    @apply bg-white/70 border border-blue-100/70 shadow-sm rounded-xl;
    backdrop-filter: blur(12px);
  }
  
  .primary-action {
    @apply bg-primary hover:bg-primary/90 text-white font-semibold py-4 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105;
  }
  
  .secondary-action {
    @apply bg-secondary hover:bg-blue-50 text-blue-800 border border-blue-100 font-medium py-3 px-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-200;
  }
  
  .metric-card {
    @apply bg-white/80 backdrop-blur-xl border border-blue-100 rounded-xl p-6 hover:shadow-md transition-shadow duration-200;
  }
  
  .quick-stat {
    @apply text-center p-4 bg-white/80 rounded-lg border border-blue-100;
  }
}

@layer base {
  .page-title {
    @apply text-3xl font-bold text-blue-900 mb-2;
  }
  
  .section-title {
    @apply text-xl font-semibold text-blue-800 mb-4;
  }
  
  .metric-value {
    @apply text-2xl font-bold text-blue-900;
  }
  
  .metric-label {
    @apply text-sm font-medium text-blue-700 uppercase tracking-wide;
  }
}

@keyframes gentle-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}
.animate-gentle-bounce {
  animation: gentle-bounce 2s ease-in-out infinite;
}
@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.animate-slide-up {
  animation: slide-up 0.4s ease-out;
}

/* Enhanced glassmorphism effects */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(2deg); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(6, 182, 212, 0.3); }
  50% { box-shadow: 0 0 40px rgba(6, 182, 212, 0.5); }
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

/* Smooth page transitions */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

@keyframes slide-in-right {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.4s ease-out;
}

@keyframes scale-in {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

/* Loading skeleton animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Hover states */
.hover-lift {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

/* Page transitions */
.page-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

/* Button states */
.btn-press {
  transition: transform 0.1s ease-out;
}

.btn-press:active {
  transform: scale(0.98);
}

/* Card animations */
.card-hover {
  transition: all 0.3s ease-out;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus states */
*:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
  transition: box-shadow 0.2s ease-out;
}

/* Loading states */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
