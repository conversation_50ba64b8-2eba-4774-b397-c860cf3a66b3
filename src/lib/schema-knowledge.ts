// Complete Database Schema Knowledge for AI Assistant
// This provides comprehensive field awareness for intelligent data extraction

export const DATABASE_SCHEMA = {
  CLIENTS: {
    description: "225 fields - Complete client management",
    core_fields: "first_name, last_name, email, mobile_phone, preferred_contact_method, marital_status",
    financial_fields: "annual_income, monthly_income, credit_score, pre_approved, pre_approval_amount, down_payment_amount, liquid_assets, retirement_funds, stocks_bonds, checking_balance, savings_balance",
    employment_fields: "employer_name, job_title, years_employed, employment_type, base_salary, overtime_income, bonus_income, commission_income",
    spouse_fields: "spouse_first_name, spouse_last_name, spouse_employer, spouse_annual_income, spouse_credit_score",
    property_preferences: "price_range_min, price_range_max, min_bedrooms, max_bedrooms, min_bathrooms, garage_spaces_needed, desired_square_footage, neighborhood_preferences, school_district_preferences",
    timeline_fields: "timeline_to_buy, motivation_level, stage, status, priority, first_contact_date, last_contact_date",
    professional_network: "preferred_agent, preferred_lender, preferred_title_company, attorney_name, insurance_agent",
    all_225_fields: "Complete schema includes identification, addresses, employment history, financial assets/debts, property preferences, legal status, communication preferences, transaction history"
  },
  
  PROPERTIES: {
    description: "38 fields - MLS property data with photos",
    fields: "list_price, close_price, original_price, address_street_complete, address_city, address_zip_code, bedrooms_and_possible_bedrooms, full_bathrooms, partial_bathrooms, square_footage, lot_size_sq_ft, lot_size_acres, garage_spaces, pool, year_built_details, architectural_style, listing_number, dom, status, on_market_date, close_date, listing_date, property_type, heating, cooling, exterior_features, interior_features, public_remarks, subdivision, photo_url"
  },
  
  TRANSACTIONS: {
    description: "51 fields - Complete transaction workflow",
    fields: "client_id, property_id, purchase_price, down_payment, loan_amount, interest_rate, loan_type, lender_name, title_company, escrow_company, agent_name, contract_date, inspection_date, appraisal_date, loan_approval_date, closing_date, status, contingencies, earnest_money, closing_costs"
  },
  
  APPOINTMENTS: {
    description: "Core scheduling fields",
    fields: "client_id, appointment_type, scheduled_date, property_id, status, duration_minutes, location, notes, agent_id"
  },
  
  OFFERS: {
    description: "12 fields - Offer management",
    fields: "client_id, property_id, offer_amount, earnest_money, financing_type, closing_date, contingencies, expiration_date, status, counter_offer_amount"
  },
  
  PROFESSIONAL_NETWORK: {
    brokers_agents: "15 fields - name, license_number, brokerage, phone, email, specialization",
    lenders: "13 fields - name, company, phone, email, loan_types, rates",
    title_companies: "11 fields - company_name, contact_person, phone, email, services",
    escrow_companies: "15 fields - company_name, escrow_officer, phone, email, fee_structure"
  }
};

export const DATA_EXTRACTION_PRIORITIES = {
  HIGH_PRIORITY: [
    "first_name", "last_name", "email", "mobile_phone", "annual_income", 
    "credit_score", "pre_approval_amount", "price_range_max", "timeline_to_buy"
  ],
  MEDIUM_PRIORITY: [
    "employer_name", "down_payment_amount", "min_bedrooms", "neighborhood_preferences",
    "spouse_first_name", "spouse_annual_income", "motivation_level"
  ],
  CONTEXTUAL: [
    "All 225+ fields available for extraction based on email content",
    "Financial fields prioritized for loan applications",
    "Property preferences prioritized for search requests",
    "Professional network fields for referrals"
  ]
};