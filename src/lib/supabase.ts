import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pfcdqrxnjyarhueofrsn.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBmY2Rxcnhuanlhcmh1ZW9mcnNuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MTUyMzEsImV4cCI6MjA2NDM5MTIzMX0.04ZvxzZn43utA1SNnqTvhjquhI801gNDcH-rJTMbIzA'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Hybrid data fetching functions
export const getClients = async () => {
  const { data, error } = await supabase
    .from('clients')
    .select('*')
    .order('last_name')
  
  if (error) {
    console.error('Error fetching clients:', error)
    return []
  }
  
  return data
}

export const getProperties = async () => {
  const { data, error } = await supabase
    .from('properties')
    .select('*')
    .order('property_address')
  
  if (error) {
    console.error('Error fetching properties:', error)
    return []
  }
  
  return data
}

export const getTransactions = async () => {
  const { data, error } = await supabase
    .from('transactions')
    .select('*')
    .order('created_at', { ascending: false })
  
  if (error) {
    console.error('Error fetching transactions:', error)
    return []
  }
  
  return data
}

export const getDashboardStats = async () => {
  const [clients, properties, transactions] = await Promise.all([
    supabase.from('clients').select('id', { count: 'exact' }),
    supabase.from('properties').select('id', { count: 'exact' }),
    supabase.from('transactions').select('id', { count: 'exact' })
  ])

  return {
    clients: clients.count || 0,
    properties: properties.count || 0,
    transactions: transactions.count || 0
  }
}