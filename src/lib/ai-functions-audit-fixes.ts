import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://pfcdqrxnjyarhueofrsn.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBmY2Rxcnhuanlhcmh1ZW9mcnNuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MTUyMzEsImV4cCI6MjA2NDM5MTIzMX0.04ZvxzZn43utA1SNnqTvhjquhI801gNDcH-rJTMbIzA'
);

// Data validation and sanitization utilities
export class DataValidator {
  static sanitizeString(value: any, maxLength?: number): string | null {
    if (value === null || value === undefined || value === '') return null;
    let str = String(value).trim();
    if (maxLength && str.length > maxLength) {
      str = str.substring(0, maxLength);
    }
    return str || null;
  }

  static sanitizeNumber(value: any): number | null {
    if (value === null || value === undefined || value === '') return null;
    const num = Number(value);
    return isNaN(num) ? null : num;
  }

  static sanitizeInteger(value: any): number | null {
    const num = this.sanitizeNumber(value);
    return num !== null ? Math.floor(num) : null;
  }

  static sanitizeBoolean(value: any): boolean | null {
    if (value === null || value === undefined || value === '') return null;
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
      const lower = value.toLowerCase().trim();
      if (lower === 'true' || lower === 'yes' || lower === '1') return true;
      if (lower === 'false' || lower === 'no' || lower === '0') return false;
    }
    return null;
  }

  static sanitizeEmail(value: any): string | null {
    const email = this.sanitizeString(value);
    if (!email) return null;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) ? email : null;
  }

  static sanitizePhone(value: any): string | null {
    const phone = this.sanitizeString(value);
    if (!phone) return null;
    // Remove all non-digit characters for storage
    const digits = phone.replace(/\D/g, '');
    return digits.length >= 10 ? digits : null;
  }

  static validateEnum(value: any, validValues: string[]): string | null {
    const str = this.sanitizeString(value);
    if (!str) return null;
    return validValues.includes(str) ? str : null;
  }
}

// Database constraint checking utilities
export class ConstraintChecker {
  static async checkClientExists(client_id: number): Promise<boolean> {
    const { data, error } = await supabase
      .from('clients')
      .select('id')
      .eq('id', client_id)
      .single();
    return !error && data !== null;
  }

  static async checkPropertyExists(property_id: number): Promise<boolean> {
    const { data, error } = await supabase
      .from('properties')
      .select('id')
      .eq('id', property_id)
      .single();
    return !error && data !== null;
  }

  static async checkEmailUnique(email: string, excludeClientId?: number): Promise<boolean> {
    let query = supabase
      .from('clients')
      .select('id')
      .eq('email', email);
    
    if (excludeClientId) {
      query = query.neq('id', excludeClientId);
    }

    const { data, error } = await query;
    return !error && (!data || data.length === 0);
  }
}

// Enhanced parameter validation for each function
export const AIFunctionValidators = {
  searchClients: (params: any) => {
    const query = DataValidator.sanitizeString(params.query);
    if (!query || query.length < 2) {
      throw new Error('Search query must be at least 2 characters long');
    }
    const limit = DataValidator.sanitizeInteger(params.limit);
    if (limit && (limit < 1 || limit > 50)) {
      throw new Error('Limit must be between 1 and 50');
    }
    return { query, limit: limit || 5 };
  },

  getClientDetails: (params: any) => {
    const client_id = DataValidator.sanitizeInteger(params.client_id);
    if (!client_id || client_id < 1) {
      throw new Error('Valid client_id is required');
    }
    return { client_id };
  },

  addNewClient: (params: any) => {
    // Required fields validation
    const first_name = DataValidator.sanitizeString(params.first_name, 100);
    const last_name = DataValidator.sanitizeString(params.last_name, 100);
    
    if (!first_name) throw new Error('first_name is required and cannot be empty');
    if (!last_name) throw new Error('last_name is required and cannot be empty');

    // Optional fields with validation
    const cleanParams: any = { first_name, last_name };

    if (params.email) {
      const email = DataValidator.sanitizeEmail(params.email);
      if (!email) throw new Error('Invalid email format');
      cleanParams.email = email;
    }

    if (params.mobile_phone) {
      const mobile_phone = DataValidator.sanitizePhone(params.mobile_phone);
      if (!mobile_phone) throw new Error('Invalid phone number format');
      cleanParams.mobile_phone = mobile_phone;
    }

    if (params.price_range_max) {
      const price_range_max = DataValidator.sanitizeNumber(params.price_range_max);
      if (price_range_max && price_range_max < 0) {
        throw new Error('price_range_max must be positive');
      }
      if (price_range_max) cleanParams.price_range_max = price_range_max;
    }

    if (params.min_bedrooms) {
      const min_bedrooms = DataValidator.sanitizeInteger(params.min_bedrooms);
      if (min_bedrooms && (min_bedrooms < 0 || min_bedrooms > 20)) {
        throw new Error('min_bedrooms must be between 0 and 20');
      }
      if (min_bedrooms) cleanParams.min_bedrooms = min_bedrooms;
    }

    if (params.motivation_level) {
      const motivation_level = DataValidator.validateEnum(
        params.motivation_level, 
        ['High', 'Medium', 'Low', 'Urgent']
      );
      if (params.motivation_level && !motivation_level) {
        throw new Error('motivation_level must be High, Medium, Low, or Urgent');
      }
      if (motivation_level) cleanParams.motivation_level = motivation_level;
    }

    if (params.neighborhood_preferences) {
      const neighborhood_preferences = DataValidator.sanitizeString(params.neighborhood_preferences);
      if (neighborhood_preferences) cleanParams.neighborhood_preferences = neighborhood_preferences;
    }

    return cleanParams;
  },

  updateClientInfo: async (params: any) => {
    const client_id = DataValidator.sanitizeInteger(params.client_id);
    if (!client_id || client_id < 1) {
      throw new Error('Valid client_id is required');
    }

    // Check if client exists
    const clientExists = await ConstraintChecker.checkClientExists(client_id);
    if (!clientExists) {
      throw new Error(`Client with ID ${client_id} does not exist`);
    }

    if (!params.updates || typeof params.updates !== 'object') {
      throw new Error('updates object is required');
    }

    const cleanUpdates: any = {};

    // Validate each update field
    if (params.updates.email !== undefined) {
      if (params.updates.email === '') {
        cleanUpdates.email = null;
      } else {
        const email = DataValidator.sanitizeEmail(params.updates.email);
        if (!email) throw new Error('Invalid email format');
        
        // Check email uniqueness
        const isUnique = await ConstraintChecker.checkEmailUnique(email, client_id);
        if (!isUnique) throw new Error('Email already exists for another client');
        
        cleanUpdates.email = email;
      }
    }

    if (params.updates.mobile_phone !== undefined) {
      if (params.updates.mobile_phone === '') {
        cleanUpdates.mobile_phone = null;
      } else {
        const mobile_phone = DataValidator.sanitizePhone(params.updates.mobile_phone);
        if (!mobile_phone) throw new Error('Invalid phone number format');
        cleanUpdates.mobile_phone = mobile_phone;
      }
    }

    // Numeric fields
    ['price_range_max', 'price_range_min'].forEach(field => {
      if (params.updates[field] !== undefined) {
        if (params.updates[field] === '') {
          cleanUpdates[field] = null;
        } else {
          const value = DataValidator.sanitizeNumber(params.updates[field]);
          if (value !== null && value < 0) {
            throw new Error(`${field} must be positive`);
          }
          cleanUpdates[field] = value;
        }
      }
    });

    // Integer fields
    if (params.updates.min_bedrooms !== undefined) {
      if (params.updates.min_bedrooms === '') {
        cleanUpdates.min_bedrooms = null;
      } else {
        const min_bedrooms = DataValidator.sanitizeInteger(params.updates.min_bedrooms);
        if (min_bedrooms !== null && (min_bedrooms < 0 || min_bedrooms > 20)) {
          throw new Error('min_bedrooms must be between 0 and 20');
        }
        cleanUpdates.min_bedrooms = min_bedrooms;
      }
    }

    // Enum fields
    if (params.updates.motivation_level !== undefined) {
      if (params.updates.motivation_level === '') {
        cleanUpdates.motivation_level = null;
      } else {
        const motivation_level = DataValidator.validateEnum(
          params.updates.motivation_level,
          ['High', 'Medium', 'Low', 'Urgent']
        );
        if (!motivation_level) {
          throw new Error('motivation_level must be High, Medium, Low, or Urgent');
        }
        cleanUpdates.motivation_level = motivation_level;
      }
    }

    // String fields
    ['neighborhood_preferences', 'timeline_to_buy', 'notes'].forEach(field => {
      if (params.updates[field] !== undefined) {
        const value = DataValidator.sanitizeString(params.updates[field]);
        cleanUpdates[field] = value;
      }
    });

    if (Object.keys(cleanUpdates).length === 0) {
      throw new Error('No valid updates provided');
    }

    return { client_id, updates: cleanUpdates };
  },

  scheduleAppointment: async (params: any) => {
    const client_id = DataValidator.sanitizeInteger(params.client_id);
    if (!client_id || client_id < 1) {
      throw new Error('Valid client_id is required');
    }

    // Check if client exists
    const clientExists = await ConstraintChecker.checkClientExists(client_id);
    if (!clientExists) {
      throw new Error(`Client with ID ${client_id} does not exist`);
    }

    const appointment_type = DataValidator.validateEnum(
      params.appointment_type,
      ['Consultation', 'Property Viewing', 'Contract Signing', 'Follow-up', 'Other']
    );
    if (!appointment_type) {
      throw new Error('appointment_type must be one of: Consultation, Property Viewing, Contract Signing, Follow-up, Other');
    }

    // Validate date format (YYYY-MM-DD)
    const date = DataValidator.sanitizeString(params.date);
    if (!date || !/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      throw new Error('date must be in YYYY-MM-DD format');
    }

    // Validate time format (HH:MM)
    const time = DataValidator.sanitizeString(params.time);
    if (!time || !/^\d{2}:\d{2}$/.test(time)) {
      throw new Error('time must be in HH:MM format');
    }

    // Validate date is not in the past
    const appointmentDate = new Date(`${date}T${time}:00`);
    if (appointmentDate < new Date()) {
      throw new Error('Appointment cannot be scheduled in the past');
    }

    let property_id = null;
    if (params.property_id) {
      property_id = DataValidator.sanitizeInteger(params.property_id);
      if (property_id && property_id < 1) {
        throw new Error('Invalid property_id');
      }
      if (property_id) {
        const propertyExists = await ConstraintChecker.checkPropertyExists(property_id);
        if (!propertyExists) {
          throw new Error(`Property with ID ${property_id} does not exist`);
        }
      }
    }

    const notes = DataValidator.sanitizeString(params.notes) || '';

    return {
      client_id,
      appointment_type,
      date,
      time,
      property_id,
      notes
    };
  },

  searchProperties: (params: any) => {
    const cleanParams: any = {};

    if (params.max_price) {
      const max_price = DataValidator.sanitizeNumber(params.max_price);
      if (max_price && max_price < 0) {
        throw new Error('max_price must be positive');
      }
      if (max_price) cleanParams.max_price = max_price;
    }

    if (params.min_bedrooms) {
      const min_bedrooms = DataValidator.sanitizeInteger(params.min_bedrooms);
      if (min_bedrooms && (min_bedrooms < 0 || min_bedrooms > 20)) {
        throw new Error('min_bedrooms must be between 0 and 20');
      }
      if (min_bedrooms) cleanParams.min_bedrooms = min_bedrooms;
    }

    if (params.city) {
      const city = DataValidator.sanitizeString(params.city);
      if (city) cleanParams.city = city;
    }

    const limit = DataValidator.sanitizeInteger(params.limit);
    if (limit && (limit < 1 || limit > 100)) {
      throw new Error('limit must be between 1 and 100');
    }
    cleanParams.limit = limit || 10;

    return cleanParams;
  },

  getPropertyDetails: (params: any) => {
    const property_id = DataValidator.sanitizeInteger(params.property_id);
    if (!property_id || property_id < 1) {
      throw new Error('Valid property_id is required');
    }
    return { property_id };
  },

  findPropertiesForClient: async (params: any) => {
    const client_id = DataValidator.sanitizeInteger(params.client_id);
    if (!client_id || client_id < 1) {
      throw new Error('Valid client_id is required');
    }

    // Check if client exists
    const clientExists = await ConstraintChecker.checkClientExists(client_id);
    if (!clientExists) {
      throw new Error(`Client with ID ${client_id} does not exist`);
    }

    const stretch_budget = DataValidator.sanitizeBoolean(params.stretch_budget) || false;

    return { client_id, stretch_budget };
  },

  getMarketStats: (params: any) => {
    const cleanParams: any = {};

    if (params.city) {
      const city = DataValidator.sanitizeString(params.city);
      if (city) cleanParams.city = city;
    }

    return cleanParams;
  },

  extractClientData: (params: any) => {
    const raw_text = DataValidator.sanitizeString(params.raw_text);
    if (!raw_text || raw_text.length < 10) {
      throw new Error('raw_text must be at least 10 characters long');
    }

    let client_id = null;
    if (params.client_id) {
      client_id = DataValidator.sanitizeInteger(params.client_id);
      if (client_id && client_id < 1) {
        throw new Error('Invalid client_id');
      }
    }

    const extraction_focus = DataValidator.validateEnum(
      params.extraction_focus,
      ['financial', 'employment', 'preferences', 'contact', 'spouse', 'comprehensive']
    );

    return {
      raw_text,
      client_id,
      extraction_focus: extraction_focus || 'comprehensive'
    };
  }
};

// Error handling utility
export class AIFunctionError extends Error {
  constructor(
    message: string,
    public functionName: string,
    public validationErrors: string[] = [],
    public constraintViolations: string[] = []
  ) {
    super(message);
    this.name = 'AIFunctionError';
  }
}