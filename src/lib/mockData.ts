// Mock Data Layer for Lovable Portfolio CRM
// Based on real Supabase data from existing CRM system

import { realClientsData } from '@/data/realClientsData';
import { realPropertiesData } from '@/data/realPropertiesData';

export interface Client {
  id: number
  first_name: string
  last_name: string
  email?: string | null
  mobile_phone?: string | null
  street_address?: string | null
  city?: string | null
  state?: string | null
  zip_code?: string | null
  client_type?: string | null
  status?: string | null
  stage?: string | null
  rating?: number | null
  priority?: string | null
  price_range_min?: string
  price_range_max?: string
  pre_approval_amount?: string
  lender_name?: string
  property_type_preference?: string
  min_bedrooms?: number
  timeline_to_buy?: string
  motivation_level?: string
  last_contact_date?: string
  created_at?: string | null
  notes?: string
  // Computed fields for property matching
  name?: string
  budget?: number
  preferences?: {
    preferredAreas?: string[]
    propertyType?: string
    minBedrooms?: number
    minSquareFeet?: number
    features?: string[]
  }
  updated_at?: string | null
}

export interface Property {
  id: string
  address_street_complete: string
  address_city: string
  address_zip_code: string
  list_price: string
  square_footage: number
  bedrooms_and_possible_bedrooms: number
  full_bathrooms: string
  partial_bathrooms: string
  garage_spaces: number
  lot_size_acres: string
  year_built_details: string
  architectural_style: string
  property_type: string
  status: string
  dom: number
  public_remarks: string
  photo_url: string
  listing_date: string
  subdivision: string
  listing_number: string
  original_price: string
  pending_date: string | null
  close_date: string | null
  close_price: string
  cooling: string
  heating: string
  fireplace_features: string
  parking_features: string
  exterior_features: string
  pool: string
  pool_features: string
  // Additional fields for matching
  year_built?: number
  county?: string
  view?: string
}

export interface Transaction {
  id: number
  transaction_type: string
  property_id: number
  buyer_client_id: number
  seller_client_id?: number
  purchase_price: string
  offer_date: string
  closing_date: string
  down_payment_amount: string
  loan_amount: string
  status: 'pending' | 'closed' | 'cancelled'
  notes?: string
}

export interface Appointment {
  id: number
  client_id: number
  appointment_type: string
  scheduled_date: string
  notes?: string
  status: 'scheduled' | 'completed' | 'cancelled'
}

export interface Offer {
  id: number
  property_id: number
  client_id: number
  offer_amount: string
  offer_date: string
  status: 'pending' | 'accepted' | 'rejected' | 'countered'
  notes?: string
}

export interface DashboardMetrics {
  totalClients: number
  activeListings: number
  pendingTransactions: number
  closedDealsThisMonth: number
  totalVolume: string
  averageDaysOnMarket: number
  conversionRate: number
}

export interface MockDataStructure {
  clients: Client[]
  properties: Property[]
  transactions: Transaction[]
  appointments: Appointment[]
  offers: Offer[]
  metrics: DashboardMetrics
}

// Real client data from Supabase (all 42 clients)
const MOCK_CLIENTS: Client[] = realClientsData as Client[]

// Original array replaced with import - keeping for reference
const OLD_MOCK_CLIENTS: Client[] = [
  {
    id: 10,
    first_name: "Michael",
    last_name: "Richmond",
    email: "<EMAIL>",
    mobile_phone: "******-319-2354x770",
    street_address: "350 Eric Ways Apt. 159",
    city: "Sacramento",
    state: "CA",
    zip_code: "74250",
    client_type: "buyer",
    status: "prospect",
    stage: "active",
    rating: 4,
    priority: "normal",
    price_range_min: "260134.00",
    price_range_max: "408782.00",
    pre_approval_amount: "490538.00",
    lender_name: "Wells Fargo",
    property_type_preference: "Single Family",
    min_bedrooms: 3,
    timeline_to_buy: "3-6 months",
    motivation_level: "Medium",
    last_contact_date: "2025-06-10 18:33:27.088923",
    created_at: "2024-07-29 15:03:22.505273",
    notes: "Interested in luxury properties with good school districts."
  },
  {
    id: 6,
    first_name: "Melissa",
    last_name: "Bradley",
    email: "<EMAIL>",
    mobile_phone: "************",
    street_address: "277 Steven Land Apt. 673",
    city: "Sacramento",
    state: "CA",
    zip_code: "77664",
    client_type: "buyer",
    status: "active",
    stage: "qualified",
    rating: 4,
    priority: "high",
    price_range_min: "400000.00",
    price_range_max: "600000.00",
    pre_approval_amount: "698378.00",
    lender_name: "Quicken Loans",
    property_type_preference: "Single Family",
    min_bedrooms: 3,
    timeline_to_buy: "3-6 months",
    motivation_level: "High",
    last_contact_date: "2025-06-10 18:33:38.679808",
    created_at: "2024-06-23 14:29:13.99167"
  },
  {
    id: 9,
    first_name: "Jillian",
    last_name: "Shaw",
    email: "<EMAIL>",
    mobile_phone: "385-975-8485x251",
    street_address: "15704 Steven Plains",
    city: "Auburn",
    state: "CA",
    zip_code: "57341",
    client_type: "investor",
    status: "prospect",
    stage: "active",
    rating: 4,
    priority: "high",
    price_range_min: "1314820.00",
    price_range_max: "2066146.00",
    pre_approval_amount: "2479375.00",
    lender_name: "Bank of America",
    property_type_preference: "Condo",
    min_bedrooms: 3,
    timeline_to_buy: "6-12 months",
    motivation_level: "High",
    last_contact_date: "2025-06-10 18:34:12.635172",
    created_at: "2025-03-13 16:02:57.57404",
    notes: "High-net-worth investor looking for luxury condos."
  },
  {
    id: 13,
    first_name: "Taylor",
    last_name: "Jones",
    email: "<EMAIL>",
    mobile_phone: "902-966-6200x586",
    street_address: "92018 Matthew Wells",
    city: "Grass Valley",
    state: "CA",
    zip_code: "27263",
    client_type: "investor",
    status: "qualified",
    stage: "lead",
    rating: 4,
    priority: "high",
    price_range_min: "1258946.00",
    price_range_max: "1978344.00",
    pre_approval_amount: "2374012.00",
    lender_name: "Quicken Loans",
    property_type_preference: "Single Family",
    min_bedrooms: 4,
    timeline_to_buy: "6-12 months",
    motivation_level: "Medium",
    last_contact_date: "2025-06-10 18:34:09.871773",
    created_at: "2024-12-31 18:04:02.082297"
  },
  {
    id: 14,
    first_name: "Dennis",
    last_name: "Clark",
    email: "<EMAIL>",
    mobile_phone: "************",
    street_address: "5923 Arnold Drive",
    city: "Nevada City",
    state: "CA",
    zip_code: "58358",
    client_type: "investor",
    status: "active",
    stage: "active",
    rating: 3,
    priority: "normal",
    price_range_min: "2080701.00",
    price_range_max: "3269673.00",
    pre_approval_amount: "3923607.00",
    lender_name: "Local Credit Union",
    property_type_preference: "Multi-Family",
    min_bedrooms: 2,
    timeline_to_buy: "1-3 months",
    motivation_level: "Medium",
    last_contact_date: "2025-06-10 18:34:08.802875",
    created_at: "2024-11-18 15:06:16.902622"
  }
]

// Real property data from Supabase (sample of 5 from 413 total)
const MOCK_PROPERTIES: Property[] = realPropertiesData;

// Original mock properties replaced with real MLS data
const OLD_MOCK_PROPERTIES: Property[] = [
  {
    id: 8,
    address_street_complete: "13383 Bass Trail",
    address_city: "Grass Valley",
    address_zip_code: "95945",
    list_price: "1399000.0",
    square_footage: 5159,
    bedrooms_and_possible_bedrooms: 4,
    full_bathrooms: "3.0",
    partial_bathrooms: "1.0",
    garage_spaces: 4,
    lot_size_acres: "7.26",
    year_built_details: "1994",
    architectural_style: "Colonial,Traditional",
    property_type: "Residential",
    status: "Active",
    dom: 705,
    public_remarks: "Spacious family home on 7.26+/- acres in gated Subdivision. 4 bedroom, 3 bath with large bedroom suite on the main level. Formal living and dining rooms, kitchen breakfast bar, large laundry room. This home and only one other home share the use of the 2+/- acre, private lake.",
    photo_url: "/photos/223040162_1.jpg",
    listing_date: "2023-05-04",
    subdivision: "Live Oak Estates"
  },
  {
    id: 9,
    address_street_complete: "14446 Perimeter",
    address_city: "Grass Valley",
    address_zip_code: "95949",
    list_price: "1150000.0",
    square_footage: 2140,
    bedrooms_and_possible_bedrooms: 3,
    full_bathrooms: "3.0",
    partial_bathrooms: "0.0",
    garage_spaces: 2,
    lot_size_acres: "12.28",
    year_built_details: "2002",
    architectural_style: "Contemporary,Craftsman",
    property_type: "Residential",
    status: "Active",
    dom: 574,
    public_remarks: "12.28+/- acres. Gated privacy in an estate setting. Geothermal heat and solar amenities. Water features, koi pond, rose gardens, maples & decorative plants and trees. An incredible landscaped setting with colorful views from every window.",
    photo_url: "/photos/223108138_1.jpg",
    listing_date: "2023-11-03"
  },
  {
    id: 10,
    address_street_complete: "506 Liberty Ct",
    address_city: "Grass Valley",
    address_zip_code: "95945",
    list_price: "649000.0",
    square_footage: 1992,
    bedrooms_and_possible_bedrooms: 3,
    full_bathrooms: "2.0",
    partial_bathrooms: "0.0",
    garage_spaces: 2,
    lot_size_acres: "0.13",
    year_built_details: "New",
    architectural_style: "Contemporary,Craftsman",
    property_type: "Residential",
    status: "Active",
    dom: 591,
    public_remarks: "Brand New 3 bed, 2 ba home in Grass Valley on a corner lot. Gourmet kitchen - granite countertops, sleek white cabinets, & top-of-the-line GE stainless steel appliances. Owned solar panels & HOA-maintained landscaping ensure low-maintenance bliss.",
    photo_url: "/photos/223099858_1.jpg",
    listing_date: "2023-10-27",
    subdivision: "Timberwood Estates"
  },
  {
    id: 11,
    address_street_complete: "607 Cold Spring Ct",
    address_city: "Grass Valley",
    address_zip_code: "95945",
    list_price: "649000.0",
    square_footage: 1992,
    bedrooms_and_possible_bedrooms: 3,
    full_bathrooms: "2.0",
    partial_bathrooms: "0.0",
    garage_spaces: 2,
    lot_size_acres: "0.14",
    year_built_details: "New",
    architectural_style: "Contemporary,Craftsman",
    property_type: "Residential",
    status: "Active",
    dom: 591,
    public_remarks: "Brand new 3 bed, 2 bath stunner in Grass Valley. Open-concept luxury, featuring a gourmet kitchen with granite counters, white cabinets, and top-of-the-line GE stainless steel appliances. PLUS owned solar AND HOA-maintained front landscaping.",
    photo_url: "/photos/223102263_1.jpg",
    listing_date: "2023-10-27",
    subdivision: "Timberwood Estates"
  },
  {
    id: 79,
    address_street_complete: "17324 Derby Way",
    address_city: "Penn Valley",
    address_zip_code: "95946",
    list_price: "349000.0",
    square_footage: 1095,
    bedrooms_and_possible_bedrooms: 2,
    full_bathrooms: "1.0",
    partial_bathrooms: "0.0",
    garage_spaces: 0,
    lot_size_acres: "20.0",
    year_built_details: "1978",
    architectural_style: "Dome",
    property_type: "Residential",
    status: "Active",
    dom: 130,
    public_remarks: "The Dome House is a total fixer upper with tremendous upside potential. Whether you are looking for a sweat equity opportunity or a Fix and Flip opportunity, you should check out this property.",
    photo_url: "/photos/225012218_1.jpg",
    listing_date: "2025-01-31"
  }
]

// Mock appointment data for dashboard
const MOCK_APPOINTMENTS: Appointment[] = [
  {
    id: 1,
    client_id: 6,
    appointment_type: "Property Showing",
    scheduled_date: "2025-06-18T10:00:00Z",
    notes: "Showing luxury condo at downtown location",
    status: "scheduled"
  },
  {
    id: 2,
    client_id: 9,
    appointment_type: "Consultation",
    scheduled_date: "2025-06-18T14:30:00Z",
    notes: "Investment strategy discussion",
    status: "scheduled"
  },
  {
    id: 3,
    client_id: 10,
    appointment_type: "Contract Signing",
    scheduled_date: "2025-06-19T09:00:00Z",
    notes: "Finalizing purchase agreement",
    status: "scheduled"
  },
  {
    id: 4,
    client_id: 13,
    appointment_type: "Property Tour",
    scheduled_date: "2025-06-19T16:00:00Z",
    notes: "Multi-family property inspection",
    status: "scheduled"
  }
]

// Mock offer data for dashboard
const MOCK_OFFERS: Offer[] = [
  {
    id: 1,
    property_id: 8,
    client_id: 9,
    offer_amount: "1350000.00",
    offer_date: "2025-06-15",
    status: "pending",
    notes: "Cash offer, flexible closing date"
  },
  {
    id: 2,
    property_id: 10,
    client_id: 6,
    offer_amount: "625000.00",
    offer_date: "2025-06-16",
    status: "pending",
    notes: "Contingent on inspection"
  },
  {
    id: 3,
    property_id: 79,
    client_id: 14,
    offer_amount: "320000.00",
    offer_date: "2025-06-12",
    status: "accepted",
    notes: "Fixer-upper investment opportunity"
  }
]

// Real transaction data from Supabase
const MOCK_TRANSACTIONS: Transaction[] = [
  {
    id: 4,
    transaction_type: "purchase",
    property_id: 92,
    buyer_client_id: 2,
    purchase_price: "752000.00",
    offer_date: "2025-06-10",
    closing_date: "2025-08-15",
    down_payment_amount: "150400.00",
    loan_amount: "601600.00",
    status: "pending"
  },
  {
    id: 5,
    transaction_type: "purchase", 
    property_id: 5,
    buyer_client_id: 16,
    seller_client_id: 3,
    purchase_price: "4275000.00",
    offer_date: "2025-06-08",
    closing_date: "2025-07-25",
    down_payment_amount: "1282500.00",
    loan_amount: "2992500.00",
    status: "pending",
    notes: "Luxury estate on 171 acres with 6757 sq ft home, pool, 3-car garage. French Normandy architecture."
  }
]

// Calculated dashboard metrics based on real data
const MOCK_DASHBOARD_METRICS: DashboardMetrics = {
  totalClients: 41,
  activeListings: 413,
  pendingTransactions: 2,
  closedDealsThisMonth: 8,
  totalVolume: "$12,847,000",
  averageDaysOnMarket: 456,
  conversionRate: 23.5
}

// Mock data functions that replace Supabase calls
export const mockData: MockDataStructure = {
  clients: MOCK_CLIENTS,
  properties: MOCK_PROPERTIES,
  transactions: MOCK_TRANSACTIONS,
  appointments: MOCK_APPOINTMENTS,
  offers: MOCK_OFFERS,
  metrics: MOCK_DASHBOARD_METRICS
}

// Mock functions to replace Supabase client calls
export const mockSupabaseClient = {
  from: (table: string) => ({
    select: (fields?: string) => ({
      data: table === 'clients' ? MOCK_CLIENTS : 
            table === 'properties' ? MOCK_PROPERTIES :
            table === 'transactions' ? MOCK_TRANSACTIONS :
            table === 'appointments' ? MOCK_APPOINTMENTS :
            table === 'offers' ? MOCK_OFFERS : [],
      error: null
    }),
    insert: (data: any) => ({
      data: data,
      error: null
    }),
    update: (data: any) => ({
      match: (criteria: any) => ({
        data: data,
        error: null
      })
    }),
    delete: () => ({
      match: (criteria: any) => ({
        data: null,
        error: null
      })
    })
  })
}

// Enhanced dashboard functions that replace complex Supabase queries
export const mockDashboardFunctions = {
  // Replace fetchDashboardStats function
  getDashboardStats: () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Count active properties
    const activeListings = MOCK_PROPERTIES.filter(p => p.status === 'Active').length;
    
    // Count recent clients (simulate new leads from today)
    const newLeads = MOCK_CLIENTS.filter(c => {
      const createdDate = new Date(c.created_at);
      return createdDate >= today;
    }).length || 2; // Default to 2 for demo
    
    // Count today's appointments
    const appointments = MOCK_APPOINTMENTS.filter(a => {
      const appointmentDate = new Date(a.scheduled_date);
      return appointmentDate.toDateString() === today.toDateString();
    }).length;
    
    // Count pending offers
    const pendingOffers = MOCK_OFFERS.filter(o => o.status === 'pending').length;
    
    return {
      activeListings,
      newLeads,
      appointments,
      pendingOffers,
    };
  },
  
  // Replace fetchUrgentTasks function
  getUrgentTasks: () => {
    const now = new Date();
    return MOCK_APPOINTMENTS
      .filter(appointment => new Date(appointment.scheduled_date) >= now)
      .sort((a, b) => new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime())
      .slice(0, 4)
      .map(appointment => ({
        id: appointment.id,
        appointment_type: appointment.appointment_type,
        scheduled_date: appointment.scheduled_date,
        notes: appointment.notes
      }));
  }
};

// Export function to get mock clients with computed fields
export const getMockClients = () => {
  return MOCK_CLIENTS.map(client => ({
    ...client,
    name: `${client.first_name} ${client.last_name}`,
    budget: parseFloat(client.price_range_max || client.pre_approval_amount || "0"),
    preferences: {
      preferredAreas: client.city ? [client.city] : [],
      propertyType: client.property_type_preference || "Single Family",
      minBedrooms: client.min_bedrooms || 2,
      minSquareFeet: undefined,
      features: []
    }
  }));
};

// Export function to get mock properties
export const getMockProperties = () => MOCK_PROPERTIES;

export default mockData;
