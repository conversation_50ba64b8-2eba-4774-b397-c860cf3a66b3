import { Client, Property } from './mockData';

export interface PropertyMatch {
  property: Property;
  matchScore: number;
  matchReasons: string[];
}

export function calculatePropertyMatches(client: Client, properties: Property[]): PropertyMatch[] {
  const matches: PropertyMatch[] = [];

  properties.forEach(property => {
    let score = 0;
    const reasons: string[] = [];

    // Budget matching (most important - 40% weight)
    const price = parseFloat(property.list_price);
    if (price <= client.budget) {
      const budgetUtilization = price / client.budget;
      if (budgetUtilization >= 0.7 && budgetUtilization <= 1.0) {
        score += 40;
        reasons.push("Perfect budget match");
      } else if (budgetUtilization >= 0.5) {
        score += 30;
        reasons.push("Good budget match");
      } else {
        score += 20;
        reasons.push("Within budget");
      }
    }

    // Location matching (30% weight)
    const preferredAreas = client.preferences.preferredAreas || [];
    if (preferredAreas.some(area => 
      property.address_city.toLowerCase().includes(area.toLowerCase()) ||
      property.subdivision?.toLowerCase().includes(area.toLowerCase())
    )) {
      score += 30;
      reasons.push("Preferred location");
    } else if (preferredAreas.length === 0) {
      // If no preference specified, give partial credit
      score += 15;
    }

    // Property type matching (10% weight)
    const clientPropertyType = client.preferences.propertyType?.toLowerCase();
    const propertyType = property.property_type?.toLowerCase();
    
    if (clientPropertyType && propertyType) {
      if (
        (clientPropertyType.includes('single') && propertyType.includes('single')) ||
        (clientPropertyType.includes('condo') && propertyType.includes('condo')) ||
        (clientPropertyType.includes('townhouse') && propertyType.includes('townhouse')) ||
        (clientPropertyType === propertyType)
      ) {
        score += 10;
        reasons.push("Property type match");
      }
    } else {
      // No preference specified
      score += 5;
    }

    // Bedroom count (10% weight)
    if (client.preferences.minBedrooms && property.bedrooms_and_possible_bedrooms >= client.preferences.minBedrooms) {
      score += 10;
      reasons.push(`${property.bedrooms_and_possible_bedrooms} bedrooms (meets requirement)`);
    } else if (!client.preferences.minBedrooms) {
      score += 5;
    }

    // Square footage (5% weight)
    if (client.preferences.minSquareFeet && property.square_footage >= client.preferences.minSquareFeet) {
      score += 5;
      reasons.push(`${property.square_footage.toLocaleString()} sq ft (meets requirement)`);
    } else if (!client.preferences.minSquareFeet) {
      score += 2.5;
    }

    // Additional features (5% weight)
    const features = client.preferences.features || [];
    let featureMatches = 0;
    
    if (features.includes('pool') && property.pool) {
      featureMatches++;
    }
    if (features.includes('garage') && property.garage_spaces > 0) {
      featureMatches++;
    }
    if (features.includes('view') && property.view) {
      featureMatches++;
    }
    
    if (featureMatches > 0) {
      score += (5 * featureMatches) / Math.max(features.length, 1);
      reasons.push(`${featureMatches} desired features`);
    }

    // Only include properties with a reasonable match score
    if (score >= 25) {
      matches.push({
        property,
        matchScore: Math.min(score, 100),
        matchReasons: reasons
      });
    }
  });

  // Sort by match score descending
  return matches.sort((a, b) => b.matchScore - a.matchScore);
}

export function getTopMatchesForClient(client: Client, properties: Property[], limit: number = 5): PropertyMatch[] {
  const allMatches = calculatePropertyMatches(client, properties);
  return allMatches.slice(0, limit);
}