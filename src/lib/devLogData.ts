export interface DevLogItem {
  id: string;
  title: string;
  status: 'pending' | 'in-progress' | 'completed';
  type: 'bug' | 'feature' | 'enhancement';
  priority: 'high' | 'medium' | 'low';
  component: string;
  location?: string; // e.g., "ClientDossier.tsx - Notes section"
  description: string;
  visualIndicator?: string; // e.g., "⚠️ tooltip on save button"
  dateIdentified: string;
  dateCompleted?: string;
  workLog?: string;
  result?: string;
}

// This data mirrors what's in our development-log.md
export const devLogItems: DevLogItem[] = [
  // Pending AI implementation features
  {
    id: 'ai-homepage-integration',
    title: 'Homepage AI Integration',
    status: 'pending',
    type: 'feature',
    priority: 'high',
    component: 'Index page',
    location: 'Hero section search bar',
    description: 'Replace main search bar with AI chat interface for primary AI-driven interaction',
    dateIdentified: '2025-06-18',
  },
  {
    id: 'ai-note-adding',
    title: 'AI Note Adding Capability',
    status: 'pending',
    type: 'feature',
    priority: 'high',
    component: 'AI Service',
    location: 'aiService.ts action handlers',
    description: 'Enable AI to create and update client notes via natural language commands',
    dateIdentified: '2025-06-18',
  },
  {
    id: 'ai-appointment-scheduling',
    title: 'AI Appointment Scheduling',
    status: 'pending',
    type: 'feature',
    priority: 'high',
    component: 'AI Service + Schedule',
    location: 'aiService.ts + Schedule page integration',
    description: 'Allow AI to create calendar appointments through natural language parsing',
    dateIdentified: '2025-06-18',
  },
  {
    id: 'ai-smart-linking',
    title: 'AI Smart Page Linking',
    status: 'pending',
    type: 'enhancement',
    priority: 'high',
    component: 'AI Service',
    location: 'AI response formatting',
    description: 'AI provides direct navigation links to client profiles and property pages',
    dateIdentified: '2025-06-18',
  },
  {
    id: 'map-integration',
    title: 'Map Integration for Property Location Section',
    status: 'pending',
    type: 'enhancement',
    priority: 'medium',
    component: 'PropertyDetail.tsx',
    location: 'Location card (lines 241-258)',
    description: 'Replace placeholder "Map View" with interactive Leaflet.js + OpenStreetMap integration (no API keys required)',
    visualIndicator: '⚠️ AlertCircle icon with tooltip explaining planned feature',
    dateIdentified: '2025-06-19',
  },
  
  // Newest completed items first
  {
    id: 'ai-chat-integration',
    title: 'Real AI Chat Integration with Gemini 2.5 Flash',
    status: 'completed',
    type: 'feature',
    priority: 'high',
    component: 'AI Service + Chat UI',
    location: 'src/services/aiService.ts, src/components/AIChat.tsx',
    description: 'Complete AI assistant integration with real Gemini API and CRM data access',
    dateIdentified: '2025-06-18',
    dateCompleted: '2025-06-18',
    result: 'Fully functioning AI assistant with conversation memory, entity extraction, and smart data queries',
    workLog: 'Implemented Gemini 2.5 Flash API, floating chat widget, data integration, and comprehensive testing',
  },
  {
    id: 'data-cleanup',
    title: 'Clean Up Properties and Phone Numbers',
    status: 'completed',
    type: 'enhancement',
    priority: 'medium',
    component: 'Data cleanup script',
    location: 'cleanup-data.cjs',
    description: 'Remove properties without real photos and normalize phone number formats',
    dateIdentified: '2025-06-18',
    dateCompleted: '2025-06-18',
    result: 'Removed 5 properties with placeholder images; normalized 30 phone numbers to (XXX) XXX-XXXX format',
    workLog: 'Created cleanup script to process mock data files',
  },
  {
    id: 'sidebar-navigation',
    title: 'Fix Sidebar Navigation Issues',
    status: 'completed',
    type: 'bug',
    priority: 'high',
    component: 'LuxurySidebar.tsx',
    location: 'Navigation routes and quick actions',
    description: 'Multiple sidebar links pointing to wrong pages or 404s',
    dateIdentified: '2025-06-18',
    dateCompleted: '2025-06-18',
    result: 'All sidebar links now navigate correctly - no more 404s or wrong page destinations',
    workLog: 'Fixed mismatched navigation labels and updated quick actions to use existing routes',
  },
  {
    id: 'notes-save',
    title: 'Notes Save Functionality',
    status: 'completed',
    type: 'bug',
    priority: 'medium',
    component: 'ClientDossier.tsx',
    location: 'Notes section - Save button',
    description: "Save button doesn't persist notes changes",
    dateIdentified: '2025-06-18',
    dateCompleted: '2025-06-18',
    result: 'Notes now save to localStorage and persist between sessions',
    workLog: 'Implemented localStorage save/load with toast notifications',
  },
  {
    id: 'dev-log-page',
    title: 'Development Log Page Implementation',
    status: 'completed',
    type: 'feature',
    priority: 'medium',
    component: 'DevLog.tsx',
    location: 'New page component',
    description: 'Create visual development log page for bug tracking and showcasing iterative development',
    dateIdentified: '2025-06-18',
    dateCompleted: '2025-06-18',
    result: 'Dev Log accessible from sidebar with stats and bug tracking',
    workLog: 'Created page with stats, pending/completed items, and professional styling',
  },
  {
    id: 'visual-warning',
    title: 'Visual Warning Indicator Implementation',
    status: 'completed',
    type: 'enhancement',
    priority: 'low',
    component: 'ClientDossier.tsx',
    location: 'lines 34, 313-332',
    description: 'Added AlertCircle icon and tooltip to save button warning users that save functionality is not implemented',
    dateIdentified: '2025-06-18',
    dateCompleted: '2025-06-18',
    result: 'Yellow warning icon with tooltip successfully displays on save button',
  },
  {
    id: 'notes-typing',
    title: 'Notes Bug Fix',
    status: 'completed',
    type: 'bug',
    priority: 'high',
    component: 'ClientDossier.tsx',
    location: 'line 52',
    description: 'Fixed useEffect dependency causing notes to reset on every render',
    dateIdentified: '2025-06-18',
    dateCompleted: '2025-06-18',
    result: 'Characters no longer disappear when typing',
    workLog: 'Changed useEffect dependency from [client] to [client?.id, client?.notes]',
  },
];

export function getDevLogStats() {
  const pending = devLogItems.filter(item => item.status === 'pending').length;
  const inProgress = devLogItems.filter(item => item.status === 'in-progress').length;
  const completed = devLogItems.filter(item => item.status === 'completed').length;
  
  return {
    pending,
    inProgress,
    completed,
    total: devLogItems.length,
    completionRate: Math.round((completed / devLogItems.length) * 100),
  };
}