// Fallback for when @google/genai package is not available
class MockGoogleGenAI {
  constructor() {}
  
  models = {
    generateContent: async () => ({
      text: "I'm in demo mode since the Google AI package isn't installed yet. Once you run 'npm install', I'll be able to provide real AI responses!"
    })
  };
}
import { realClientsData } from '@/data/realClientsData';
import { realPropertiesData } from '@/data/realPropertiesData';
import { validateAIResponse, extractNamesFromSearchResults } from '@/utils/responseValidator';
import { autoGenerateLinks } from '@/utils/entityRouteMapper';

// Types for our AI system
export interface AIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

export interface ExtractedEntity {
  type: 'client' | 'property' | 'email' | 'phone' | 'amount' | 'date' | 'form_type';
  value: string;
  confidence: number;
}

export interface CRMOperation {
  id: string;
  type: 'create_client' | 'update_client' | 'find_client' | 'create_property' | 'create_transaction';
  data: Record<string, any>;
  status: 'pending' | 'confirmed' | 'executed' | 'cancelled';
  requiresConfirmation: boolean;
}

export interface AIResponse {
  message: string;
  extractedEntities?: ExtractedEntity[];
  suggestedOperation?: CRMOperation;
  intent?: 'form_generation' | 'search' | 'update' | 'create' | 'general_inquiry';
}

class AIService {
  private ai: any = null;
  private initialized = false;
  private requestCount = 0;
  private requestResetTime = Date.now() + 3600000; // 1 hour from now
  private isDemo = false;

  constructor() {
    this.initialize();
  }

  private async initialize() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    const isEnabled = import.meta.env.VITE_ENABLE_AI_CHAT === 'true';

    if (!isEnabled) {
      console.log('AI Chat is disabled');
      return;
    }

    if (!apiKey) {
      console.error('Missing VITE_GEMINI_API_KEY in environment variables');
      this.ai = new MockGoogleGenAI();
      this.isDemo = true;
      this.initialized = true;
      return;
    }

    try {
      // Try to import and use real Google AI
      const { GoogleGenAI } = await import('@google/genai');
      this.ai = new GoogleGenAI({ apiKey });
      this.initialized = true;
      console.log('AI Service initialized successfully with gemini-2.0-flash-exp');
    } catch (error) {
      console.warn('Google GenAI package not available, using demo mode:', error);
      this.ai = new MockGoogleGenAI();
      this.isDemo = true;
      this.initialized = true;
    }
  }

  private checkRateLimit(): boolean {
    const now = Date.now();
    const rateLimit = parseInt(import.meta.env.VITE_AI_RATE_LIMIT || '20');

    // Reset counter if hour has passed
    if (now > this.requestResetTime) {
      this.requestCount = 0;
      this.requestResetTime = now + 3600000;
    }

    if (this.requestCount >= rateLimit) {
      return false;
    }

    this.requestCount++;
    return true;
  }

  private extractEntities(text: string): ExtractedEntity[] {
    const entities: ExtractedEntity[] = [];

    // Email extraction
    const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const emails = text.match(emailRegex);
    if (emails) {
      emails.forEach(email => {
        entities.push({ type: 'email', value: email, confidence: 0.95 });
      });
    }

    // Phone extraction (handles various formats)
    const phoneRegex = /(\+?1[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g;
    const phones = text.match(phoneRegex);
    if (phones) {
      phones.forEach(phone => {
        // Normalize to (XXX) XXX-XXXX format
        const digits = phone.replace(/\D/g, '');
        if (digits.length >= 10) {
          const normalized = `(${digits.slice(-10, -7)}) ${digits.slice(-7, -4)}-${digits.slice(-4)}`;
          entities.push({ type: 'phone', value: normalized, confidence: 0.9 });
        }
      });
    }

    // Amount extraction
    const amountRegex = /\$[\d,]+(?:\.\d{2})?|\b\d+k\b/gi;
    const amounts = text.match(amountRegex);
    if (amounts) {
      amounts.forEach(amount => {
        let value = amount.replace(/[$,]/g, '');
        if (amount.toLowerCase().includes('k')) {
          value = (parseFloat(value) * 1000).toString();
        }
        entities.push({ type: 'amount', value, confidence: 0.85 });
      });
    }

    // Form type detection
    const formTypes = {
      'purchase agreement': 'RPA-CA',
      'buyer representation': 'BR',
      'listing agreement': 'RLA',
      'disclosure': 'TDS',
      'inspection': 'REI'
    };

    Object.entries(formTypes).forEach(([key, value]) => {
      if (text.toLowerCase().includes(key)) {
        entities.push({ type: 'form_type', value, confidence: 0.8 });
      }
    });

    return entities;
  }

  private buildSystemPrompt(): string {
    const totalClients = realClientsData.length;
    const totalProperties = realPropertiesData.length;
    
    return `[DEBUG: Code updated at ${new Date().toLocaleTimeString()}] You are Narissa's friendly and intelligent AI assistant for her real estate business. You have access to her CRM database and can help with both business tasks and casual conversation.

PERSONALITY:
- Be warm, conversational, and helpful
- Think through problems step-by-step
- Use reasoning to provide insightful answers
- Be professional but not robotic
    
DATABASE OVERVIEW:
- ${totalClients} clients in the system
- ${totalProperties} properties available

CLIENT SCHEMA (exact field names):
- first_name, last_name (required)
- city, state, street_address, zip_code
- email, mobile_phone
- client_type (buyer/seller/investor/both)
- price_range_min, price_range_max
- priority (high/medium/low)
- status (active/inactive/prospect)

PROPERTY SCHEMA (exact field names):
- address_street_complete (NOT "address")
- address_city (NOT "city")
- address_zip_code
- list_price (NOT "price") 
- bedrooms_and_possible_bedrooms (NOT "bedrooms")
- full_bathrooms, partial_bathrooms
- square_footage
- property_type

CAPABILITIES:
1. Search and filter clients by:
   - Name, location (city/state)
   - Budget range (price_range_min/max)
   - Client type (buyer/seller/investor)
   - Status (active/inactive/prospect)
   - Priority (high/medium/low)
   - Pre-approval status

2. Search and filter properties by:
   - Location (address, city, zip)
   - Price range
   - Bedrooms/bathrooms
   - Property type
   - Square footage
   - Listing status

3. Match clients with suitable properties based on:
   - Budget compatibility
   - Location preferences
   - Bedroom requirements
   - Property type preferences

4. Provide insights on:
   - Client activity and engagement
   - Market trends in specific areas
   - Property availability
   - Recent client interactions

THINKING APPROACH:
When answering questions, follow this process:
1. First, understand what the user is really asking for
2. If it's a search query, check the SEARCH RESULTS section
3. Think about the most helpful way to respond
4. Provide thoughtful, conversational answers

IMPORTANT GUIDELINES:
- Base your answers on the search results when discussing specific clients/properties
- Don't invent client names that don't exist in the database
- If you can't find something, explain why and suggest alternatives
- Feel free to have normal conversations beyond just CRM tasks

When responding to queries:
- Use ONLY client names and property addresses from the SEARCH RESULTS section
- If searching for a name yields no results, say "I couldn't find any clients with that name in the database"
- All statements must be directly traceable to the search results provided
- Provide specific details (prices, contact info, dates) ONLY if they appear in search results
- Suggest next actions (schedule showing, send listing, follow up)
- Be precise with numbers and data

IMPORTANT - Navigation Links:
When mentioning clients, properties, or pages, include clickable navigation links using this format:
- For clients: [[View Profile|client:First Last]] (e.g., [[View Aaron's profile|client:Aaron Wilson]])
- For properties: [[View Property|property:123 Main St]] (use the street address)
- For pages: [[Go to Schedule|page:schedule]] (valid pages: schedule, dashboard, clients, properties, reports)

CRITICAL - Name Matching Rules:
- ONLY provide links for clients that ACTUALLY EXIST in the database
- NEVER make up or hallucinate client names
- If searching by last name (e.g., "Jennings") and no match exists, say "I couldn't find any clients with the last name Jennings"
- If user searches for a name that doesn't exist exactly (e.g., "Aaron McDaniels" when only "Aaron Mcdaniel" exists):
  - DO NOT provide a link to the close match
  - Instead say: "I couldn't find 'Aaron McDaniels'. Did you mean Aaron Mcdaniel? [[View Aaron Mcdaniel's profile|client:Aaron Mcdaniel]]"
- ALWAYS use the EXACT name from the database when creating links
- NEVER create a link like [[View Michael Jennings|client:Michael Jennings]] if Michael Jennings doesn't exist

Examples:
- "I found Aaron Wilson matching your search. [[View his profile|client:Aaron Wilson]] to see full details."
- "I couldn't find 'John Smiths'. Did you mean John Smith? [[View John Smith's profile|client:John Smith]]"
- "The property at 123 Main St is available. [[View this property|property:123 Main St]] for photos and details."

For entity extraction, identify:
- Names (first and last)
- Email addresses
- Phone numbers (normalize to (XXX) XXX-XXXX format)
- Property addresses
- Price amounts
- Dates
- Form types (purchase agreement, listing agreement, etc.)`;
  }

  // Data access methods for AI to use
  private searchClients(criteria: {
    name?: string;
    city?: string;
    type?: string;
    minBudget?: number;
    maxBudget?: number;
    status?: string;
    priority?: string;
  }) {
    return realClientsData.filter(client => {
      if (criteria.name) {
        const fullName = `${client.first_name} ${client.last_name}`.toLowerCase();
        const searchName = criteria.name.toLowerCase();
        // Check full name, first name, or last name
        if (!fullName.includes(searchName) && 
            !client.first_name.toLowerCase().includes(searchName) && 
            !client.last_name.toLowerCase().includes(searchName)) {
          return false;
        }
      }
      if (criteria.city && client.city?.toLowerCase() !== criteria.city.toLowerCase()) {
        return false;
      }
      if (criteria.type && client.client_type !== criteria.type) {
        return false;
      }
      if (criteria.minBudget && parseFloat(client.price_range_min || '0') < criteria.minBudget) {
        return false;
      }
      if (criteria.maxBudget && parseFloat(client.price_range_max || '0') > criteria.maxBudget) {
        return false;
      }
      if (criteria.status && client.status !== criteria.status) {
        return false;
      }
      if (criteria.priority && client.priority !== criteria.priority) {
        return false;
      }
      return true;
    });
  }

  private searchProperties(criteria: {
    address?: string;
    city?: string;
    minPrice?: number;
    maxPrice?: number;
    minBeds?: number;
    propertyType?: string;
  }) {
    console.log('searchProperties called with criteria:', criteria);
    return realPropertiesData.filter(property => {
      if (criteria.address) {
        const searchAddress = criteria.address.toLowerCase();
        const propertyAddress = property.address_street_complete.toLowerCase();
        if (!propertyAddress.includes(searchAddress)) {
          return false;
        }
      }
      if (criteria.city) {
        const propertyCity = property.address_city.toLowerCase();
        if (propertyCity !== criteria.city.toLowerCase()) {
          return false;
        }
      }
      if (criteria.minPrice) {
        const price = parseFloat(property.list_price);
        if (price < criteria.minPrice) {
          return false;
        }
      }
      if (criteria.maxPrice) {
        const price = parseFloat(property.list_price);
        if (price > criteria.maxPrice) {
          return false;
        }
      }
      if (criteria.minBeds && property.bedrooms_and_possible_bedrooms < criteria.minBeds) {
        return false;
      }
      if (criteria.propertyType && property.property_type !== criteria.propertyType) {
        return false;
      }
      return true;
    });
  }

  private formatClientSummary(client: typeof realClientsData[0]): string {
    const budget = client.price_range_max ? 
      `$${(parseFloat(client.price_range_max) / 1000).toFixed(0)}k` : 
      'Not specified';
    
    return `**${client.first_name} ${client.last_name}** - ${client.client_type || 'Client'}, ` +
           `Budget: ${budget}, ${client.city || 'Location unknown'}, ` +
           `Priority: ${client.priority || 'normal'}`;
  }

  private formatPropertySummary(property: typeof realPropertiesData[0]): string {
    const address = property.address_street_complete;
    const price = parseFloat(property.list_price) / 1000;
    const beds = property.bedrooms_and_possible_bedrooms;
    const baths = parseInt(property.full_bathrooms) + (parseInt(property.partial_bathrooms) * 0.5);
    const sqft = property.square_footage;
    
    return `**${address}** - $${price.toFixed(0)}k, ` +
           `${beds}bd/${baths}ba, ${sqft} sqft`;
  }

  async sendMessage(
    message: string, 
    conversationHistory: AIMessage[] = []
  ): Promise<AIResponse> {
    if (!this.initialized) {
      return {
        message: "AI service is not available. Please check your configuration.",
        intent: 'general_inquiry'
      };
    }

    if (!this.checkRateLimit()) {
      return {
        message: "Rate limit exceeded. Please try again later.",
        intent: 'general_inquiry'
      };
    }

    try {
      // Check if user is asking about clients or properties and provide context
      const lowerMessage = message.toLowerCase();
      let dataContext = '';
      let searchPerformed = false;
      let searchedNames: string[] = [];
      
      try {  // Inner try-catch for search operations
      
      // Detect client searches - including by name
      if (lowerMessage.includes('client') || lowerMessage.includes('buyer') || 
          lowerMessage.includes('seller') || lowerMessage.includes('investor') ||
          lowerMessage.includes('find') || lowerMessage.includes('show') ||
          lowerMessage.includes('search') || lowerMessage.includes('list') ||
          lowerMessage.includes('all') ||
          // Also search if message is just a single word (likely a name search)
          message.trim().split(' ').length <= 2) {
        
        searchPerformed = true;
        
        // Extract potential search criteria from message
        const cityMatch = message.match(/in (\w+)/i);
        const typeMatch = message.match(/(buyer|seller|investor)/i);
        const budgetMatch = message.match(/\$?(\d+)k/i);
        
        // Check for name searches - look for capitalized words OR any single word
        const nameMatches = message.match(/\b[A-Z][a-z]+\b/g) || 
                           (message.trim().split(' ').length <= 2 ? [message.trim()] : null);
        
        const searchCriteria: any = {};
        if (cityMatch) searchCriteria.city = cityMatch[1];
        if (typeMatch) searchCriteria.type = typeMatch[1].toLowerCase();
        if (budgetMatch) searchCriteria.minBudget = parseInt(budgetMatch[1]) * 1000;
        
        // Special case: "list all" should return all clients
        if (lowerMessage.includes('list all') || lowerMessage.includes('all clients')) {
          const allClients = this.searchClients({});
          if (allClients.length > 0) {
            dataContext = `\n\n=== SEARCH RESULTS ===\nAll clients in database:\n` +
              allClients.map(c => this.formatClientSummary(c)).join('\n') +
              `\n=== END SEARCH RESULTS ===`;
          }
        }
        // If we have potential names, search by name
        else if (nameMatches && nameMatches.length > 0) {
          let foundAny = false;
          // Try each capitalized word as a potential name
          for (const name of nameMatches) {
            searchedNames.push(name);
            searchCriteria.name = name;
            const matchingClients = this.searchClients(searchCriteria);
            if (matchingClients.length > 0) {
              foundAny = true;
              dataContext = `\n\n=== SEARCH RESULTS ===\nClients found in database matching "${name}":\n` +
                matchingClients.map(c => `- ${c.first_name} ${c.last_name} (${c.client_type || 'client'}, ${c.city || 'location unknown'})`).join('\n') +
                `\n=== END SEARCH RESULTS ===`;
              break;
            }
          }
          
          // If no names found, provide context about similar names
          if (!foundAny && searchedNames.length > 0) {
            // Find clients with similar last names
            const searchedLastName = searchedNames[0].toLowerCase();
            const similarClients = realClientsData.filter(client => {
              const clientLastName = client.last_name.toLowerCase();
              return clientLastName.includes(searchedLastName) || searchedLastName.includes(clientLastName);
            });
            
            dataContext = `\n\n=== SEARCH RESULTS ===\nNo clients found matching: ${searchedNames.join(', ')}\n`;
            
            if (similarClients.length > 0) {
              dataContext += `\nClients with similar names in database:\n`;
              dataContext += similarClients.map(c => `- ${c.first_name} ${c.last_name} (${c.client_type || 'client'}, ${c.city || 'location unknown'})`).join('\n');
            }
            
            dataContext += `\n=== END SEARCH RESULTS ===`;
          }
        } else {
          // Regular search without name
          const matchingClients = this.searchClients(searchCriteria);
          if (matchingClients.length > 0) {
            dataContext = `\n\n=== SEARCH RESULTS ===\nClients found in database:\n` +
              matchingClients.slice(0, 5).map(c => this.formatClientSummary(c)).join('\n') +
              `\n=== END SEARCH RESULTS ===`;
          } else {
            dataContext = `\n\n=== SEARCH RESULTS ===\nNo clients found matching your search criteria\n=== END SEARCH RESULTS ===`;
          }
        }
      }
      
      // SIMPLE PROPERTY SEARCH - COPY THE WORKING APPROACH
      if (lowerMessage.includes('bass') || lowerMessage.includes('trail') || 
          lowerMessage.includes('propert') || lowerMessage.includes('house') ||
          lowerMessage.includes('home') || lowerMessage.includes('address')) {
        
        searchPerformed = true;
        
        // Extract search terms from the message - keep it simple
        const words = message.toLowerCase().split(' ');
        const searchTerms = words.filter(w => 
          w.length > 2 && 
          !['the', 'show', 'find', 'search', 'property', 'properties', 'house', 'home', 'for', 'with', 'and'].includes(w)
        );
        
        console.log('Property search terms:', searchTerms);
        
        // Use EXACT same logic as the working Properties page search
        const matchingProperties = realPropertiesData.filter(property => {
          return searchTerms.some(term => {
            const searchTerm = term.toLowerCase();
            return (
              property.address_street_complete?.toLowerCase().includes(searchTerm) ||
              property.address_city?.toLowerCase().includes(searchTerm) ||
              property.subdivision?.toLowerCase().includes(searchTerm) ||
              property.architectural_style?.toLowerCase().includes(searchTerm)
            );
          });
        });
        
        console.log(`Found ${matchingProperties.length} properties`);
        
        // ADD DEBUG INFO TO THE RESPONSE
        dataContext += `\n\n=== DEBUG INFO ===\n`;
        dataContext += `Search triggered: YES\n`;
        dataContext += `Search terms extracted: [${searchTerms.join(', ')}]\n`;
        dataContext += `Total properties in database: ${realPropertiesData.length}\n`;
        dataContext += `Properties found: ${matchingProperties.length}\n`;
        dataContext += `Code version: DEBUG-v3-WORKING (${new Date().toLocaleTimeString()})\n`;
        dataContext += `=== END DEBUG ===\n`;
        
        if (matchingProperties.length > 0) {
          dataContext += `\n\n=== SEARCH RESULTS ===\nProperties found in database:\n` +
            matchingProperties.slice(0, 5).map(p => this.formatPropertySummary(p)).join('\n') +
            `\n=== END SEARCH RESULTS ===`;
        } else {
          dataContext += `\n\n=== SEARCH RESULTS ===\nNo properties found matching your search\n=== END SEARCH RESULTS ===`;
        }
      }
      
      } catch (searchError) {
        console.error('Error during search operations:', searchError);
        dataContext = '\n\n=== SEARCH ERROR ===\nI encountered an error while searching. Please try rephrasing your query.\n=== END ERROR ===';
      }
      
      // Pre-generation check: Only skip AI for very specific name searches with no results
      // Removed - let AI handle all responses based on search results

      // Build conversation context for new SDK
      const contents = [
        this.buildSystemPrompt(),
        ...conversationHistory.map(msg => msg.content),
        message + dataContext
      ].join('\n\n');

      // Get AI response using Google AI SDK
      const response = await this.ai!.models.generateContent({
        model: 'gemini-2.0-flash-exp', // Using Gemini 2.0 Flash experimental
        contents: contents,
        config: {
          temperature: 0.7,  // Increased for more natural conversation
          topK: 40,          // More diverse token selection
          topP: 0.9,         // More creative responses
          maxOutputTokens: 8192, // Increased for longer responses
        }
      });
      
      let aiText = response.text;

      // Post-generation validation: DISABLED - was too aggressive
      // TODO: Reimplement with better logic that doesn't break legitimate UI text

      // Auto-generate links in AI response
      aiText = autoGenerateLinks(aiText);

      // Extract entities from user message
      const extractedEntities = this.extractEntities(message);

      // Determine intent
      let intent: AIResponse['intent'] = 'general_inquiry';
      
      if (lowerMessage.includes('create') || lowerMessage.includes('add') || lowerMessage.includes('new')) {
        intent = 'create';
      } else if (lowerMessage.includes('find') || lowerMessage.includes('search') || lowerMessage.includes('show')) {
        intent = 'search';
      } else if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('modify')) {
        intent = 'update';
      } else if (lowerMessage.includes('form') || lowerMessage.includes('agreement') || lowerMessage.includes('contract')) {
        intent = 'form_generation';
      }

      // Build suggested operation if applicable
      let suggestedOperation: CRMOperation | undefined;
      
      if (intent === 'create' && extractedEntities.length > 0) {
        const hasEmail = extractedEntities.find(e => e.type === 'email');
        const hasPhone = extractedEntities.find(e => e.type === 'phone');
        
        if (hasEmail || hasPhone) {
          // Extract name from message
          const nameMatch = message.match(/(?:client|contact|person|add)\s+([A-Z][a-z]+)\s+([A-Z][a-z]+)/);
          
          suggestedOperation = {
            id: `op_${Date.now()}_create_client`,
            type: 'create_client',
            data: {
              first_name: nameMatch?.[1] || '',
              last_name: nameMatch?.[2] || '',
              email: hasEmail?.value || '',
              mobile_phone: hasPhone?.value || '',
            },
            status: 'pending',
            requiresConfirmation: true
          };
        }
      }

      return {
        message: aiText,
        extractedEntities: extractedEntities.length > 0 ? extractedEntities : undefined,
        suggestedOperation,
        intent
      };

    } catch (error) {
      console.error('AI Service error:', error);
      
      // More helpful error messages based on what was being searched
      let errorMessage = "I had trouble processing that search. ";
      
      if (message.toLowerCase().includes('propert')) {
        errorMessage += "Try searching for properties by city, price range, or specific street names.";
      } else if (searchedNames && searchedNames.length > 0) {
        errorMessage += `I couldn't complete the search for "${searchedNames.join(', ')}". Try searching by first name, last name, or full name.`;
      } else {
        errorMessage += "Please try rephrasing your search or be more specific about what you're looking for.";
      }
      
      return {
        message: errorMessage,
        intent: 'general_inquiry'
      };
    }
  }

  // Simulate AI response for demo mode
  async sendDemoMessage(message: string): Promise<AIResponse> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const lowerMessage = message.toLowerCase();
    
    // Demo responses based on keywords
    if (lowerMessage.includes('find') && lowerMessage.includes('client')) {
      let demoMessage = "I found 3 clients matching your search criteria:\n\n1. **Jason Smith** - Interested in properties in Davis, budget $650k [[View Jason's profile|client:Jason Smith]]\n2. **Katherine Williams** - Cash buyer, looking for investment properties [[View Katherine's profile|client:Katherine Williams]]\n3. **Brian Farley** - Pre-approved for $750k, needs 4+ bedrooms [[View Brian's profile|client:Brian Farley]]";

      return {
        message: autoGenerateLinks(demoMessage),
        intent: 'search'
      };
    }

    if (lowerMessage.includes('create') && (lowerMessage.includes('client') || lowerMessage.includes('contact'))) {
      const entities = this.extractEntities(message);
      return {
        message: "I'll help you create a new client record. I've extracted the following information:\n\n" +
                 entities.map(e => `• ${e.type}: ${e.value}`).join('\n') +
                 "\n\nWould you like me to create this client record?",
        extractedEntities: entities,
        suggestedOperation: {
          id: `demo_${Date.now()}`,
          type: 'create_client',
          data: { demo: true },
          status: 'pending',
          requiresConfirmation: true
        },
        intent: 'create'
      };
    }

    if (lowerMessage.includes('schedule') || lowerMessage.includes('appointment')) {
      let demoMessage = "I can help you schedule an appointment. What date and time works best? I can also check for conflicts with your existing calendar. [[Go to Schedule|page:schedule]] to view your calendar.";

      return {
        message: autoGenerateLinks(demoMessage),
        intent: 'create'
      };
    }

    // Default response
    return {
      message: "I understand you're asking about: " + message + "\n\nI can help you with:\n• Finding and managing clients\n• Searching properties\n• Generating forms\n• Scheduling appointments\n\nHow can I assist you today?",
      intent: 'general_inquiry'
    };
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  getRemainingRequests(): number {
    const rateLimit = parseInt(import.meta.env.VITE_AI_RATE_LIMIT || '20');
    return Math.max(0, rateLimit - this.requestCount);
  }
}

// Export singleton instance  
export const aiService = new AIService();