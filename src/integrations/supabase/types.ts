export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      appointments: {
        Row: {
          appointment_type: string
          client_id: number
          created_at: string | null
          duration_minutes: number | null
          id: number
          location: string | null
          notes: string | null
          property_id: number | null
          scheduled_date: string
          status: string | null
          updated_at: string | null
        }
        Insert: {
          appointment_type: string
          client_id: number
          created_at?: string | null
          duration_minutes?: number | null
          id?: number
          location?: string | null
          notes?: string | null
          property_id?: number | null
          scheduled_date: string
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          appointment_type?: string
          client_id?: number
          created_at?: string | null
          duration_minutes?: number | null
          id?: number
          location?: string | null
          notes?: string | null
          property_id?: number | null
          scheduled_date?: string
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "appointments_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      brokers_agents: {
        Row: {
          agent_cellular: string | null
          agent_dre_license: string | null
          agent_email: string | null
          agent_fax: string | null
          agent_name: string
          agent_phone: string | null
          created_at: string | null
          firm_address: string | null
          firm_city: string | null
          firm_dre_license: string | null
          firm_name: string
          firm_phone: string | null
          firm_state: string | null
          firm_zip_code: string | null
          id: number
          role: string
        }
        Insert: {
          agent_cellular?: string | null
          agent_dre_license?: string | null
          agent_email?: string | null
          agent_fax?: string | null
          agent_name: string
          agent_phone?: string | null
          created_at?: string | null
          firm_address?: string | null
          firm_city?: string | null
          firm_dre_license?: string | null
          firm_name: string
          firm_phone?: string | null
          firm_state?: string | null
          firm_zip_code?: string | null
          id?: number
          role: string
        }
        Update: {
          agent_cellular?: string | null
          agent_dre_license?: string | null
          agent_email?: string | null
          agent_fax?: string | null
          agent_name?: string
          agent_phone?: string | null
          created_at?: string | null
          firm_address?: string | null
          firm_city?: string | null
          firm_dre_license?: string | null
          firm_name?: string
          firm_phone?: string | null
          firm_state?: string | null
          firm_zip_code?: string | null
          id?: number
          role?: string
        }
        Relationships: []
      }
      clients: {
        Row: {
          account_balance: number | null
          age: number | null
          agency_disclosure_date: string | null
          agency_disclosure_signed: boolean | null
          agent_brokerage: string | null
          agent_email: string | null
          agent_name: string | null
          agent_phone: string | null
          ages_of_dependents: string | null
          alimony_payments: number | null
          allows_escalation_clause: boolean | null
          allows_marketing_calls: boolean | null
          allows_marketing_emails: boolean | null
          allows_marketing_texts: boolean | null
          annual_income: number | null
          appraisal_contingency_days: number | null
          archived: boolean | null
          assigned_agent_id: number | null
          assigned_team: string | null
          attorney_name: string | null
          attorney_phone: string | null
          bank_account_type: string | null
          bank_name: string | null
          bankruptcy_discharge_date: string | null
          bankruptcy_history: boolean | null
          beneficiary_names: string | null
          best_time_to_call: string | null
          blacklisted_vendors: string | null
          bonus_income: number | null
          business_name: string | null
          buyer_agreement_date: string | null
          buying_as_entity: boolean | null
          buying_together: boolean | null
          car_payment: number | null
          cash_buyer: boolean | null
          child_support_payments: number | null
          citizenship_status: string | null
          city: string | null
          client_type: string | null
          commission_income: number | null
          contingent_on_sale: boolean | null
          contractor_references: string | null
          country: string | null
          county: string | null
          created_at: string | null
          credit_card_payments: number | null
          credit_report_auth: boolean | null
          credit_score: number | null
          credit_score_date: string | null
          current_home_owner: boolean | null
          current_monthly_payment: number | null
          current_mortgage_balance: number | null
          current_property_value: number | null
          data_sharing_consent: boolean | null
          date_of_birth: string | null
          deal_breakers: string | null
          disability_income: number | null
          disclosure_forms_date: string | null
          divorce_pending: boolean | null
          do_not_call: boolean | null
          do_not_email: boolean | null
          do_not_text: boolean | null
          documents_uploaded: boolean | null
          domestic_partnership: boolean | null
          down_payment_amount: number | null
          drivers_license_expiration: string | null
          drivers_license_number: string | null
          drivers_license_state: string | null
          earnest_money_comfort_level: number | null
          email: string | null
          email_secondary: string | null
          emergency_contact_1_address: string | null
          emergency_contact_1_email: string | null
          emergency_contact_1_name: string | null
          emergency_contact_1_phone: string | null
          emergency_contact_1_relationship: string | null
          emergency_contact_2_address: string | null
          emergency_contact_2_email: string | null
          emergency_contact_2_name: string | null
          emergency_contact_2_phone: string | null
          emergency_contact_2_relationship: string | null
          employer_address: string | null
          employer_city: string | null
          employer_name: string | null
          employer_phone: string | null
          employer_state: string | null
          employer_zip: string | null
          employment_status: string | null
          entity_formation_date: string | null
          entity_formation_state: string | null
          entity_name: string | null
          entity_tax_id: string | null
          entity_type: string | null
          escrow_company_preference: string | null
          escrow_preference: string | null
          fair_housing_training_complete: boolean | null
          fax_number: string | null
          financial_docs_on_file: boolean | null
          first_name: string
          first_time_buyer: boolean | null
          flexible_on_closing: boolean | null
          foreclosure_history: boolean | null
          full_ssn_encrypted: string | null
          gap_in_employment_reason: string | null
          garage_spaces_min: number | null
          gift_funds_amount: number | null
          gift_funds_source: string | null
          has_real_estate_agent: boolean | null
          home_inspector_preference: string | null
          home_phone: string | null
          home_warranty_interest: boolean | null
          household_income: number | null
          id: number
          id_verification_date: string | null
          id_verified: boolean | null
          income_type: string | null
          inspection_contingency_days: number | null
          insurance_agent_name: string | null
          insurance_agent_phone: string | null
          insurance_company: string | null
          internal_notes: string | null
          investment_accounts: number | null
          investment_properties_count: number | null
          investor_type: string | null
          job_title: string | null
          landlord_name: string | null
          landlord_phone: string | null
          last_contact_date: string | null
          last_name: string
          lead_source: string | null
          lender_contact: string | null
          lender_email: string | null
          lender_name: string | null
          lender_phone: string | null
          loan_contingency_days: number | null
          loan_type: string | null
          looking_to_invest: boolean | null
          lot_size_min: number | null
          maiden_name: string | null
          mail_preference: string | null
          mailing_address: string | null
          mailing_address_2: string | null
          mailing_city: string | null
          mailing_country: string | null
          mailing_same_as_physical: boolean | null
          mailing_state: string | null
          mailing_zip: string | null
          marital_status: string | null
          marketing_campaign: string | null
          married_date: string | null
          max_escalation_amount: number | null
          middle_name: string | null
          military_service: boolean | null
          min_bathrooms: number | null
          min_bedrooms: number | null
          min_square_feet: number | null
          mobile_phone: string | null
          monthly_debt_payments: number | null
          monthly_income: number | null
          monthly_rent_payment: number | null
          motivation_level: string | null
          moving_company_used: string | null
          must_have_features: string | null
          need_to_sell_current: boolean | null
          neighborhood_preferences: string | null
          next_follow_up_date: string | null
          notes: string | null
          number_of_dependents: number | null
          occupation: string | null
          ok_to_leave_voicemail: boolean | null
          other_income_sources: string | null
          other_monthly_payments: number | null
          other_names_used: string | null
          overtime_income: number | null
          own_or_rent: string | null
          passport_country: string | null
          passport_expiration: string | null
          passport_number: string | null
          pay_frequency: string | null
          personality_notes: string | null
          pet_types: string | null
          pets: boolean | null
          poa_email: string | null
          poa_name: string | null
          poa_phone: string | null
          pool_required: boolean | null
          power_of_attorney: boolean | null
          pre_approval_amount: number | null
          pre_approval_date: string | null
          pre_approval_expiration: string | null
          pre_approval_letter_on_file: boolean | null
          pre_approved: boolean | null
          preferred_closing_date: string | null
          preferred_communication_style: string | null
          preferred_contact_method: string | null
          preferred_language: string | null
          preferred_name: string | null
          preferred_phone: string | null
          preferred_vendors: string | null
          prenuptial_agreement: boolean | null
          previous_employer: string | null
          previous_monthly_income: number | null
          previous_occupation: string | null
          previous_years_employed: number | null
          price_range_max: number | null
          price_range_min: number | null
          priority: string | null
          privacy_policy_accepted: boolean | null
          privacy_policy_date: string | null
          proof_of_funds_date: string | null
          proof_of_funds_verified: boolean | null
          properties_owned_count: number | null
          property_type_preference: string | null
          rating: number | null
          recording_consent: boolean | null
          red_flags: string | null
          referral_source: string | null
          requires_interpreter: boolean | null
          retirement_accounts: number | null
          retirement_income: number | null
          savings_balance: number | null
          school_district_preferences: string | null
          self_employed: boolean | null
          short_sale_history: boolean | null
          signed_buyer_agreement: boolean | null
          signed_disclosure_forms: boolean | null
          sms_consent: boolean | null
          social_media_contact: string | null
          special_instructions: string | null
          spouse_credit_score: number | null
          spouse_date_of_birth: string | null
          spouse_email: string | null
          spouse_employer: string | null
          spouse_first_name: string | null
          spouse_income: number | null
          spouse_last_name: string | null
          spouse_occupation: string | null
          spouse_phone: string | null
          spouse_ssn_last_four: string | null
          ssn_last_four: string | null
          stage: string | null
          state: string | null
          status: string | null
          street_address: string | null
          street_address_2: string | null
          student_loan_payments: number | null
          style_preferences: string | null
          suffix: string | null
          supervisor_name: string | null
          supervisor_phone: string | null
          tags: string | null
          timeline_to_buy: string | null
          timeline_to_sell: string | null
          title_company_preference: string | null
          title_insurance_preference: string | null
          trust_name: string | null
          trustee_name: string | null
          updated_at: string | null
          viewing_availability: string | null
          visa_expiration: string | null
          visa_type: string | null
          wants_dual_agency: boolean | null
          wire_fraud_warning_date: string | null
          wire_fraud_warning_given: boolean | null
          work_phone: string | null
          years_at_address: number | null
          years_employed: number | null
          zip_code: string | null
        }
        Insert: {
          account_balance?: number | null
          age?: number | null
          agency_disclosure_date?: string | null
          agency_disclosure_signed?: boolean | null
          agent_brokerage?: string | null
          agent_email?: string | null
          agent_name?: string | null
          agent_phone?: string | null
          ages_of_dependents?: string | null
          alimony_payments?: number | null
          allows_escalation_clause?: boolean | null
          allows_marketing_calls?: boolean | null
          allows_marketing_emails?: boolean | null
          allows_marketing_texts?: boolean | null
          annual_income?: number | null
          appraisal_contingency_days?: number | null
          archived?: boolean | null
          assigned_agent_id?: number | null
          assigned_team?: string | null
          attorney_name?: string | null
          attorney_phone?: string | null
          bank_account_type?: string | null
          bank_name?: string | null
          bankruptcy_discharge_date?: string | null
          bankruptcy_history?: boolean | null
          beneficiary_names?: string | null
          best_time_to_call?: string | null
          blacklisted_vendors?: string | null
          bonus_income?: number | null
          business_name?: string | null
          buyer_agreement_date?: string | null
          buying_as_entity?: boolean | null
          buying_together?: boolean | null
          car_payment?: number | null
          cash_buyer?: boolean | null
          child_support_payments?: number | null
          citizenship_status?: string | null
          city?: string | null
          client_type?: string | null
          commission_income?: number | null
          contingent_on_sale?: boolean | null
          contractor_references?: string | null
          country?: string | null
          county?: string | null
          created_at?: string | null
          credit_card_payments?: number | null
          credit_report_auth?: boolean | null
          credit_score?: number | null
          credit_score_date?: string | null
          current_home_owner?: boolean | null
          current_monthly_payment?: number | null
          current_mortgage_balance?: number | null
          current_property_value?: number | null
          data_sharing_consent?: boolean | null
          date_of_birth?: string | null
          deal_breakers?: string | null
          disability_income?: number | null
          disclosure_forms_date?: string | null
          divorce_pending?: boolean | null
          do_not_call?: boolean | null
          do_not_email?: boolean | null
          do_not_text?: boolean | null
          documents_uploaded?: boolean | null
          domestic_partnership?: boolean | null
          down_payment_amount?: number | null
          drivers_license_expiration?: string | null
          drivers_license_number?: string | null
          drivers_license_state?: string | null
          earnest_money_comfort_level?: number | null
          email?: string | null
          email_secondary?: string | null
          emergency_contact_1_address?: string | null
          emergency_contact_1_email?: string | null
          emergency_contact_1_name?: string | null
          emergency_contact_1_phone?: string | null
          emergency_contact_1_relationship?: string | null
          emergency_contact_2_address?: string | null
          emergency_contact_2_email?: string | null
          emergency_contact_2_name?: string | null
          emergency_contact_2_phone?: string | null
          emergency_contact_2_relationship?: string | null
          employer_address?: string | null
          employer_city?: string | null
          employer_name?: string | null
          employer_phone?: string | null
          employer_state?: string | null
          employer_zip?: string | null
          employment_status?: string | null
          entity_formation_date?: string | null
          entity_formation_state?: string | null
          entity_name?: string | null
          entity_tax_id?: string | null
          entity_type?: string | null
          escrow_company_preference?: string | null
          escrow_preference?: string | null
          fair_housing_training_complete?: boolean | null
          fax_number?: string | null
          financial_docs_on_file?: boolean | null
          first_name: string
          first_time_buyer?: boolean | null
          flexible_on_closing?: boolean | null
          foreclosure_history?: boolean | null
          full_ssn_encrypted?: string | null
          gap_in_employment_reason?: string | null
          garage_spaces_min?: number | null
          gift_funds_amount?: number | null
          gift_funds_source?: string | null
          has_real_estate_agent?: boolean | null
          home_inspector_preference?: string | null
          home_phone?: string | null
          home_warranty_interest?: boolean | null
          household_income?: number | null
          id?: number
          id_verification_date?: string | null
          id_verified?: boolean | null
          income_type?: string | null
          inspection_contingency_days?: number | null
          insurance_agent_name?: string | null
          insurance_agent_phone?: string | null
          insurance_company?: string | null
          internal_notes?: string | null
          investment_accounts?: number | null
          investment_properties_count?: number | null
          investor_type?: string | null
          job_title?: string | null
          landlord_name?: string | null
          landlord_phone?: string | null
          last_contact_date?: string | null
          last_name: string
          lead_source?: string | null
          lender_contact?: string | null
          lender_email?: string | null
          lender_name?: string | null
          lender_phone?: string | null
          loan_contingency_days?: number | null
          loan_type?: string | null
          looking_to_invest?: boolean | null
          lot_size_min?: number | null
          maiden_name?: string | null
          mail_preference?: string | null
          mailing_address?: string | null
          mailing_address_2?: string | null
          mailing_city?: string | null
          mailing_country?: string | null
          mailing_same_as_physical?: boolean | null
          mailing_state?: string | null
          mailing_zip?: string | null
          marital_status?: string | null
          marketing_campaign?: string | null
          married_date?: string | null
          max_escalation_amount?: number | null
          middle_name?: string | null
          military_service?: boolean | null
          min_bathrooms?: number | null
          min_bedrooms?: number | null
          min_square_feet?: number | null
          mobile_phone?: string | null
          monthly_debt_payments?: number | null
          monthly_income?: number | null
          monthly_rent_payment?: number | null
          motivation_level?: string | null
          moving_company_used?: string | null
          must_have_features?: string | null
          need_to_sell_current?: boolean | null
          neighborhood_preferences?: string | null
          next_follow_up_date?: string | null
          notes?: string | null
          number_of_dependents?: number | null
          occupation?: string | null
          ok_to_leave_voicemail?: boolean | null
          other_income_sources?: string | null
          other_monthly_payments?: number | null
          other_names_used?: string | null
          overtime_income?: number | null
          own_or_rent?: string | null
          passport_country?: string | null
          passport_expiration?: string | null
          passport_number?: string | null
          pay_frequency?: string | null
          personality_notes?: string | null
          pet_types?: string | null
          pets?: boolean | null
          poa_email?: string | null
          poa_name?: string | null
          poa_phone?: string | null
          pool_required?: boolean | null
          power_of_attorney?: boolean | null
          pre_approval_amount?: number | null
          pre_approval_date?: string | null
          pre_approval_expiration?: string | null
          pre_approval_letter_on_file?: boolean | null
          pre_approved?: boolean | null
          preferred_closing_date?: string | null
          preferred_communication_style?: string | null
          preferred_contact_method?: string | null
          preferred_language?: string | null
          preferred_name?: string | null
          preferred_phone?: string | null
          preferred_vendors?: string | null
          prenuptial_agreement?: boolean | null
          previous_employer?: string | null
          previous_monthly_income?: number | null
          previous_occupation?: string | null
          previous_years_employed?: number | null
          price_range_max?: number | null
          price_range_min?: number | null
          priority?: string | null
          privacy_policy_accepted?: boolean | null
          privacy_policy_date?: string | null
          proof_of_funds_date?: string | null
          proof_of_funds_verified?: boolean | null
          properties_owned_count?: number | null
          property_type_preference?: string | null
          rating?: number | null
          recording_consent?: boolean | null
          red_flags?: string | null
          referral_source?: string | null
          requires_interpreter?: boolean | null
          retirement_accounts?: number | null
          retirement_income?: number | null
          savings_balance?: number | null
          school_district_preferences?: string | null
          self_employed?: boolean | null
          short_sale_history?: boolean | null
          signed_buyer_agreement?: boolean | null
          signed_disclosure_forms?: boolean | null
          sms_consent?: boolean | null
          social_media_contact?: string | null
          special_instructions?: string | null
          spouse_credit_score?: number | null
          spouse_date_of_birth?: string | null
          spouse_email?: string | null
          spouse_employer?: string | null
          spouse_first_name?: string | null
          spouse_income?: number | null
          spouse_last_name?: string | null
          spouse_occupation?: string | null
          spouse_phone?: string | null
          spouse_ssn_last_four?: string | null
          ssn_last_four?: string | null
          stage?: string | null
          state?: string | null
          status?: string | null
          street_address?: string | null
          street_address_2?: string | null
          student_loan_payments?: number | null
          style_preferences?: string | null
          suffix?: string | null
          supervisor_name?: string | null
          supervisor_phone?: string | null
          tags?: string | null
          timeline_to_buy?: string | null
          timeline_to_sell?: string | null
          title_company_preference?: string | null
          title_insurance_preference?: string | null
          trust_name?: string | null
          trustee_name?: string | null
          updated_at?: string | null
          viewing_availability?: string | null
          visa_expiration?: string | null
          visa_type?: string | null
          wants_dual_agency?: boolean | null
          wire_fraud_warning_date?: string | null
          wire_fraud_warning_given?: boolean | null
          work_phone?: string | null
          years_at_address?: number | null
          years_employed?: number | null
          zip_code?: string | null
        }
        Update: {
          account_balance?: number | null
          age?: number | null
          agency_disclosure_date?: string | null
          agency_disclosure_signed?: boolean | null
          agent_brokerage?: string | null
          agent_email?: string | null
          agent_name?: string | null
          agent_phone?: string | null
          ages_of_dependents?: string | null
          alimony_payments?: number | null
          allows_escalation_clause?: boolean | null
          allows_marketing_calls?: boolean | null
          allows_marketing_emails?: boolean | null
          allows_marketing_texts?: boolean | null
          annual_income?: number | null
          appraisal_contingency_days?: number | null
          archived?: boolean | null
          assigned_agent_id?: number | null
          assigned_team?: string | null
          attorney_name?: string | null
          attorney_phone?: string | null
          bank_account_type?: string | null
          bank_name?: string | null
          bankruptcy_discharge_date?: string | null
          bankruptcy_history?: boolean | null
          beneficiary_names?: string | null
          best_time_to_call?: string | null
          blacklisted_vendors?: string | null
          bonus_income?: number | null
          business_name?: string | null
          buyer_agreement_date?: string | null
          buying_as_entity?: boolean | null
          buying_together?: boolean | null
          car_payment?: number | null
          cash_buyer?: boolean | null
          child_support_payments?: number | null
          citizenship_status?: string | null
          city?: string | null
          client_type?: string | null
          commission_income?: number | null
          contingent_on_sale?: boolean | null
          contractor_references?: string | null
          country?: string | null
          county?: string | null
          created_at?: string | null
          credit_card_payments?: number | null
          credit_report_auth?: boolean | null
          credit_score?: number | null
          credit_score_date?: string | null
          current_home_owner?: boolean | null
          current_monthly_payment?: number | null
          current_mortgage_balance?: number | null
          current_property_value?: number | null
          data_sharing_consent?: boolean | null
          date_of_birth?: string | null
          deal_breakers?: string | null
          disability_income?: number | null
          disclosure_forms_date?: string | null
          divorce_pending?: boolean | null
          do_not_call?: boolean | null
          do_not_email?: boolean | null
          do_not_text?: boolean | null
          documents_uploaded?: boolean | null
          domestic_partnership?: boolean | null
          down_payment_amount?: number | null
          drivers_license_expiration?: string | null
          drivers_license_number?: string | null
          drivers_license_state?: string | null
          earnest_money_comfort_level?: number | null
          email?: string | null
          email_secondary?: string | null
          emergency_contact_1_address?: string | null
          emergency_contact_1_email?: string | null
          emergency_contact_1_name?: string | null
          emergency_contact_1_phone?: string | null
          emergency_contact_1_relationship?: string | null
          emergency_contact_2_address?: string | null
          emergency_contact_2_email?: string | null
          emergency_contact_2_name?: string | null
          emergency_contact_2_phone?: string | null
          emergency_contact_2_relationship?: string | null
          employer_address?: string | null
          employer_city?: string | null
          employer_name?: string | null
          employer_phone?: string | null
          employer_state?: string | null
          employer_zip?: string | null
          employment_status?: string | null
          entity_formation_date?: string | null
          entity_formation_state?: string | null
          entity_name?: string | null
          entity_tax_id?: string | null
          entity_type?: string | null
          escrow_company_preference?: string | null
          escrow_preference?: string | null
          fair_housing_training_complete?: boolean | null
          fax_number?: string | null
          financial_docs_on_file?: boolean | null
          first_name?: string
          first_time_buyer?: boolean | null
          flexible_on_closing?: boolean | null
          foreclosure_history?: boolean | null
          full_ssn_encrypted?: string | null
          gap_in_employment_reason?: string | null
          garage_spaces_min?: number | null
          gift_funds_amount?: number | null
          gift_funds_source?: string | null
          has_real_estate_agent?: boolean | null
          home_inspector_preference?: string | null
          home_phone?: string | null
          home_warranty_interest?: boolean | null
          household_income?: number | null
          id?: number
          id_verification_date?: string | null
          id_verified?: boolean | null
          income_type?: string | null
          inspection_contingency_days?: number | null
          insurance_agent_name?: string | null
          insurance_agent_phone?: string | null
          insurance_company?: string | null
          internal_notes?: string | null
          investment_accounts?: number | null
          investment_properties_count?: number | null
          investor_type?: string | null
          job_title?: string | null
          landlord_name?: string | null
          landlord_phone?: string | null
          last_contact_date?: string | null
          last_name?: string
          lead_source?: string | null
          lender_contact?: string | null
          lender_email?: string | null
          lender_name?: string | null
          lender_phone?: string | null
          loan_contingency_days?: number | null
          loan_type?: string | null
          looking_to_invest?: boolean | null
          lot_size_min?: number | null
          maiden_name?: string | null
          mail_preference?: string | null
          mailing_address?: string | null
          mailing_address_2?: string | null
          mailing_city?: string | null
          mailing_country?: string | null
          mailing_same_as_physical?: boolean | null
          mailing_state?: string | null
          mailing_zip?: string | null
          marital_status?: string | null
          marketing_campaign?: string | null
          married_date?: string | null
          max_escalation_amount?: number | null
          middle_name?: string | null
          military_service?: boolean | null
          min_bathrooms?: number | null
          min_bedrooms?: number | null
          min_square_feet?: number | null
          mobile_phone?: string | null
          monthly_debt_payments?: number | null
          monthly_income?: number | null
          monthly_rent_payment?: number | null
          motivation_level?: string | null
          moving_company_used?: string | null
          must_have_features?: string | null
          need_to_sell_current?: boolean | null
          neighborhood_preferences?: string | null
          next_follow_up_date?: string | null
          notes?: string | null
          number_of_dependents?: number | null
          occupation?: string | null
          ok_to_leave_voicemail?: boolean | null
          other_income_sources?: string | null
          other_monthly_payments?: number | null
          other_names_used?: string | null
          overtime_income?: number | null
          own_or_rent?: string | null
          passport_country?: string | null
          passport_expiration?: string | null
          passport_number?: string | null
          pay_frequency?: string | null
          personality_notes?: string | null
          pet_types?: string | null
          pets?: boolean | null
          poa_email?: string | null
          poa_name?: string | null
          poa_phone?: string | null
          pool_required?: boolean | null
          power_of_attorney?: boolean | null
          pre_approval_amount?: number | null
          pre_approval_date?: string | null
          pre_approval_expiration?: string | null
          pre_approval_letter_on_file?: boolean | null
          pre_approved?: boolean | null
          preferred_closing_date?: string | null
          preferred_communication_style?: string | null
          preferred_contact_method?: string | null
          preferred_language?: string | null
          preferred_name?: string | null
          preferred_phone?: string | null
          preferred_vendors?: string | null
          prenuptial_agreement?: boolean | null
          previous_employer?: string | null
          previous_monthly_income?: number | null
          previous_occupation?: string | null
          previous_years_employed?: number | null
          price_range_max?: number | null
          price_range_min?: number | null
          priority?: string | null
          privacy_policy_accepted?: boolean | null
          privacy_policy_date?: string | null
          proof_of_funds_date?: string | null
          proof_of_funds_verified?: boolean | null
          properties_owned_count?: number | null
          property_type_preference?: string | null
          rating?: number | null
          recording_consent?: boolean | null
          red_flags?: string | null
          referral_source?: string | null
          requires_interpreter?: boolean | null
          retirement_accounts?: number | null
          retirement_income?: number | null
          savings_balance?: number | null
          school_district_preferences?: string | null
          self_employed?: boolean | null
          short_sale_history?: boolean | null
          signed_buyer_agreement?: boolean | null
          signed_disclosure_forms?: boolean | null
          sms_consent?: boolean | null
          social_media_contact?: string | null
          special_instructions?: string | null
          spouse_credit_score?: number | null
          spouse_date_of_birth?: string | null
          spouse_email?: string | null
          spouse_employer?: string | null
          spouse_first_name?: string | null
          spouse_income?: number | null
          spouse_last_name?: string | null
          spouse_occupation?: string | null
          spouse_phone?: string | null
          spouse_ssn_last_four?: string | null
          ssn_last_four?: string | null
          stage?: string | null
          state?: string | null
          status?: string | null
          street_address?: string | null
          street_address_2?: string | null
          student_loan_payments?: number | null
          style_preferences?: string | null
          suffix?: string | null
          supervisor_name?: string | null
          supervisor_phone?: string | null
          tags?: string | null
          timeline_to_buy?: string | null
          timeline_to_sell?: string | null
          title_company_preference?: string | null
          title_insurance_preference?: string | null
          trust_name?: string | null
          trustee_name?: string | null
          updated_at?: string | null
          viewing_availability?: string | null
          visa_expiration?: string | null
          visa_type?: string | null
          wants_dual_agency?: boolean | null
          wire_fraud_warning_date?: string | null
          wire_fraud_warning_given?: boolean | null
          work_phone?: string | null
          years_at_address?: number | null
          years_employed?: number | null
          zip_code?: string | null
        }
        Relationships: []
      }
      conversations: {
        Row: {
          created_at: string | null
          id: string
          messages: Json
          updated_at: string | null
          user_session: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          messages?: Json
          updated_at?: string | null
          user_session: string
        }
        Update: {
          created_at?: string | null
          id?: string
          messages?: Json
          updated_at?: string | null
          user_session?: string
        }
        Relationships: []
      }
      escrow_companies: {
        Row: {
          city: string | null
          closing_date: string | null
          company_name: string
          created_at: string | null
          deposit_one: number | null
          deposit_two: number | null
          escrow_number: string | null
          fax: string | null
          id: number
          officer_email: string | null
          officer_license_number: string | null
          officer_name: string | null
          phone: string | null
          state: string | null
          street_address: string | null
          zip_code: string | null
        }
        Insert: {
          city?: string | null
          closing_date?: string | null
          company_name: string
          created_at?: string | null
          deposit_one?: number | null
          deposit_two?: number | null
          escrow_number?: string | null
          fax?: string | null
          id?: number
          officer_email?: string | null
          officer_license_number?: string | null
          officer_name?: string | null
          phone?: string | null
          state?: string | null
          street_address?: string | null
          zip_code?: string | null
        }
        Update: {
          city?: string | null
          closing_date?: string | null
          company_name?: string
          created_at?: string | null
          deposit_one?: number | null
          deposit_two?: number | null
          escrow_number?: string | null
          fax?: string | null
          id?: number
          officer_email?: string | null
          officer_license_number?: string | null
          officer_name?: string | null
          phone?: string | null
          state?: string | null
          street_address?: string | null
          zip_code?: string | null
        }
        Relationships: []
      }
      jobs: {
        Row: {
          benefits: string[] | null
          company: string
          created_at: string | null
          extracted_at: string | null
          id: number
          job_id: string
          location: string | null
          requirements: string[] | null
          salary: string | null
          source: string
          summary: string | null
          title: string
          url: string | null
        }
        Insert: {
          benefits?: string[] | null
          company: string
          created_at?: string | null
          extracted_at?: string | null
          id?: number
          job_id: string
          location?: string | null
          requirements?: string[] | null
          salary?: string | null
          source: string
          summary?: string | null
          title: string
          url?: string | null
        }
        Update: {
          benefits?: string[] | null
          company?: string
          created_at?: string | null
          extracted_at?: string | null
          id?: number
          job_id?: string
          location?: string | null
          requirements?: string[] | null
          salary?: string | null
          source?: string
          summary?: string | null
          title?: string
          url?: string | null
        }
        Relationships: []
      }
      lenders: {
        Row: {
          city: string | null
          company_name: string
          created_at: string | null
          fax: string | null
          id: number
          mortgage_type: string | null
          mortgage_type_other: string | null
          officer_cell_phone: string | null
          officer_email: string | null
          officer_name: string | null
          phone: string | null
          state: string | null
          street_address: string | null
          zip_code: string | null
        }
        Insert: {
          city?: string | null
          company_name: string
          created_at?: string | null
          fax?: string | null
          id?: number
          mortgage_type?: string | null
          mortgage_type_other?: string | null
          officer_cell_phone?: string | null
          officer_email?: string | null
          officer_name?: string | null
          phone?: string | null
          state?: string | null
          street_address?: string | null
          zip_code?: string | null
        }
        Update: {
          city?: string | null
          company_name?: string
          created_at?: string | null
          fax?: string | null
          id?: number
          mortgage_type?: string | null
          mortgage_type_other?: string | null
          officer_cell_phone?: string | null
          officer_email?: string | null
          officer_name?: string | null
          phone?: string | null
          state?: string | null
          street_address?: string | null
          zip_code?: string | null
        }
        Relationships: []
      }
      offers: {
        Row: {
          agent_id: number | null
          buyer_client_id: number
          created_at: string | null
          form_type: string
          id: number
          lender_id: number | null
          offer_terms: string | null
          pdf_path: string | null
          property_id: number
          status: string | null
          transaction_id: number | null
          updated_at: string | null
          workflow_id: string
        }
        Insert: {
          agent_id?: number | null
          buyer_client_id: number
          created_at?: string | null
          form_type: string
          id?: number
          lender_id?: number | null
          offer_terms?: string | null
          pdf_path?: string | null
          property_id: number
          status?: string | null
          transaction_id?: number | null
          updated_at?: string | null
          workflow_id: string
        }
        Update: {
          agent_id?: number | null
          buyer_client_id?: number
          created_at?: string | null
          form_type?: string
          id?: number
          lender_id?: number | null
          offer_terms?: string | null
          pdf_path?: string | null
          property_id?: number
          status?: string | null
          transaction_id?: number | null
          updated_at?: string | null
          workflow_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "offers_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "brokers_agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "offers_buyer_client_id_fkey"
            columns: ["buyer_client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "offers_lender_id_fkey"
            columns: ["lender_id"]
            isOneToOne: false
            referencedRelation: "lenders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "offers_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties_backup"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "offers_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "transactions"
            referencedColumns: ["id"]
          },
        ]
      }
      properties: {
        Row: {
          address_city: string | null
          address_street_complete: string | null
          address_zip_code: string | null
          architectural_style: string | null
          bedrooms_and_possible_bedrooms: number | null
          close_date: string | null
          close_price: number | null
          cooling: string | null
          created_at: string | null
          dom: number | null
          exterior_features: string | null
          fireplace_features: string | null
          full_bathrooms: number | null
          garage_spaces: number | null
          heating: string | null
          id: number
          levels: string | null
          list_price: number | null
          listing_date: string | null
          listing_number: string | null
          lot_size_acres: number | null
          lot_size_sq_ft: number | null
          num_of_fireplaces: number | null
          on_market_date: string | null
          original_price: number | null
          parking_features: string | null
          partial_bathrooms: number | null
          patio_and_porch_features: string | null
          pending_date: string | null
          photo_url: string | null
          pool: string | null
          pool_features: string | null
          property_type: string | null
          public_remarks: string | null
          square_footage: number | null
          status: string | null
          subdivision: string | null
          updated_at: string | null
          year_built_details: string | null
        }
        Insert: {
          address_city?: string | null
          address_street_complete?: string | null
          address_zip_code?: string | null
          architectural_style?: string | null
          bedrooms_and_possible_bedrooms?: number | null
          close_date?: string | null
          close_price?: number | null
          cooling?: string | null
          created_at?: string | null
          dom?: number | null
          exterior_features?: string | null
          fireplace_features?: string | null
          full_bathrooms?: number | null
          garage_spaces?: number | null
          heating?: string | null
          id?: number
          levels?: string | null
          list_price?: number | null
          listing_date?: string | null
          listing_number?: string | null
          lot_size_acres?: number | null
          lot_size_sq_ft?: number | null
          num_of_fireplaces?: number | null
          on_market_date?: string | null
          original_price?: number | null
          parking_features?: string | null
          partial_bathrooms?: number | null
          patio_and_porch_features?: string | null
          pending_date?: string | null
          photo_url?: string | null
          pool?: string | null
          pool_features?: string | null
          property_type?: string | null
          public_remarks?: string | null
          square_footage?: number | null
          status?: string | null
          subdivision?: string | null
          updated_at?: string | null
          year_built_details?: string | null
        }
        Update: {
          address_city?: string | null
          address_street_complete?: string | null
          address_zip_code?: string | null
          architectural_style?: string | null
          bedrooms_and_possible_bedrooms?: number | null
          close_date?: string | null
          close_price?: number | null
          cooling?: string | null
          created_at?: string | null
          dom?: number | null
          exterior_features?: string | null
          fireplace_features?: string | null
          full_bathrooms?: number | null
          garage_spaces?: number | null
          heating?: string | null
          id?: number
          levels?: string | null
          list_price?: number | null
          listing_date?: string | null
          listing_number?: string | null
          lot_size_acres?: number | null
          lot_size_sq_ft?: number | null
          num_of_fireplaces?: number | null
          on_market_date?: string | null
          original_price?: number | null
          parking_features?: string | null
          partial_bathrooms?: number | null
          patio_and_porch_features?: string | null
          pending_date?: string | null
          photo_url?: string | null
          pool?: string | null
          pool_features?: string | null
          property_type?: string | null
          public_remarks?: string | null
          square_footage?: number | null
          status?: string | null
          subdivision?: string | null
          updated_at?: string | null
          year_built_details?: string | null
        }
        Relationships: []
      }
      properties_backup: {
        Row: {
          address_city: string | null
          address_street_complete: string | null
          address_zip_code: string | null
          architectural_style: string | null
          bedrooms_and_possible_bedrooms: number | null
          close_date: string | null
          close_price: number | null
          cooling: string | null
          created_at: string | null
          dom: number | null
          exterior_features: string | null
          fireplace_features: string | null
          full_bathrooms: number | null
          garage_spaces: number | null
          heating: string | null
          id: number
          levels: string | null
          list_price: number | null
          listing_date: string | null
          listing_number: string | null
          lot_size_acres: number | null
          lot_size_sq_ft: number | null
          num_of_fireplaces: number | null
          on_market_date: string | null
          original_price: number | null
          parking_features: string | null
          partial_bathrooms: number | null
          patio_and_porch_features: string | null
          pending_date: string | null
          pool: string | null
          pool_features: string | null
          property_type: string | null
          public_remarks: string | null
          square_footage: number | null
          status: string | null
          subdivision: string | null
          updated_at: string | null
          year_built_details: string | null
        }
        Insert: {
          address_city?: string | null
          address_street_complete?: string | null
          address_zip_code?: string | null
          architectural_style?: string | null
          bedrooms_and_possible_bedrooms?: number | null
          close_date?: string | null
          close_price?: number | null
          cooling?: string | null
          created_at?: string | null
          dom?: number | null
          exterior_features?: string | null
          fireplace_features?: string | null
          full_bathrooms?: number | null
          garage_spaces?: number | null
          heating?: string | null
          id?: number
          levels?: string | null
          list_price?: number | null
          listing_date?: string | null
          listing_number?: string | null
          lot_size_acres?: number | null
          lot_size_sq_ft?: number | null
          num_of_fireplaces?: number | null
          on_market_date?: string | null
          original_price?: number | null
          parking_features?: string | null
          partial_bathrooms?: number | null
          patio_and_porch_features?: string | null
          pending_date?: string | null
          pool?: string | null
          pool_features?: string | null
          property_type?: string | null
          public_remarks?: string | null
          square_footage?: number | null
          status?: string | null
          subdivision?: string | null
          updated_at?: string | null
          year_built_details?: string | null
        }
        Update: {
          address_city?: string | null
          address_street_complete?: string | null
          address_zip_code?: string | null
          architectural_style?: string | null
          bedrooms_and_possible_bedrooms?: number | null
          close_date?: string | null
          close_price?: number | null
          cooling?: string | null
          created_at?: string | null
          dom?: number | null
          exterior_features?: string | null
          fireplace_features?: string | null
          full_bathrooms?: number | null
          garage_spaces?: number | null
          heating?: string | null
          id?: number
          levels?: string | null
          list_price?: number | null
          listing_date?: string | null
          listing_number?: string | null
          lot_size_acres?: number | null
          lot_size_sq_ft?: number | null
          num_of_fireplaces?: number | null
          on_market_date?: string | null
          original_price?: number | null
          parking_features?: string | null
          partial_bathrooms?: number | null
          patio_and_porch_features?: string | null
          pending_date?: string | null
          pool?: string | null
          pool_features?: string | null
          property_type?: string | null
          public_remarks?: string | null
          square_footage?: number | null
          status?: string | null
          subdivision?: string | null
          updated_at?: string | null
          year_built_details?: string | null
        }
        Relationships: []
      }
      service_providers: {
        Row: {
          city: string | null
          company_name: string
          coordinator_side: string | null
          created_at: string | null
          fax: string | null
          id: number
          phone: string | null
          representative_cell_phone: string | null
          representative_email: string | null
          representative_name: string | null
          service_type: string
          state: string | null
          street_address: string | null
          zip_code: string | null
        }
        Insert: {
          city?: string | null
          company_name: string
          coordinator_side?: string | null
          created_at?: string | null
          fax?: string | null
          id?: number
          phone?: string | null
          representative_cell_phone?: string | null
          representative_email?: string | null
          representative_name?: string | null
          service_type: string
          state?: string | null
          street_address?: string | null
          zip_code?: string | null
        }
        Update: {
          city?: string | null
          company_name?: string
          coordinator_side?: string | null
          created_at?: string | null
          fax?: string | null
          id?: number
          phone?: string | null
          representative_cell_phone?: string | null
          representative_email?: string | null
          representative_name?: string | null
          service_type?: string
          state?: string | null
          street_address?: string | null
          zip_code?: string | null
        }
        Relationships: []
      }
      title_companies: {
        Row: {
          city: string | null
          company_name: string
          created_at: string | null
          fax: string | null
          id: number
          officer_cell_phone: string | null
          officer_email: string | null
          officer_name: string | null
          phone: string | null
          state: string | null
          street_address: string | null
          zip_code: string | null
        }
        Insert: {
          city?: string | null
          company_name: string
          created_at?: string | null
          fax?: string | null
          id?: number
          officer_cell_phone?: string | null
          officer_email?: string | null
          officer_name?: string | null
          phone?: string | null
          state?: string | null
          street_address?: string | null
          zip_code?: string | null
        }
        Update: {
          city?: string | null
          company_name?: string
          created_at?: string | null
          fax?: string | null
          id?: number
          officer_cell_phone?: string | null
          officer_email?: string | null
          officer_name?: string | null
          phone?: string | null
          state?: string | null
          street_address?: string | null
          zip_code?: string | null
        }
        Relationships: []
      }
      transactions: {
        Row: {
          acceptance_date: string | null
          appraisal_company_id: number | null
          appraisal_contingency_date: string | null
          as_is_sale: boolean | null
          buyer_client_id: number | null
          closing_date: string | null
          contract_date: string | null
          created_at: string | null
          deposit_1st_increase: number | null
          deposit_2nd_increase: number | null
          deposit_3rd_increase: number | null
          deposit_amount: number | null
          disclosure_providers: string | null
          down_payment_amount: number | null
          down_payment_percentage: number | null
          earnest_money_amount: number | null
          escrow_company_id: number | null
          final_sale_price: number | null
          financing_contingency_date: string | null
          hoa_approval_contingency_date: string | null
          hoa_providers: string | null
          home_warranty: boolean | null
          home_warranty_providers: string | null
          homeowners_insurance_contingency_date: string | null
          id: number
          inspection_contingency_date: string | null
          interest_rate: number | null
          lender_id: number | null
          listing_agent_id: number | null
          listing_broker_id: number | null
          loan_amount: number | null
          loan_term_years: number | null
          notes: string | null
          offer_date: string | null
          offer_expiration_date: string | null
          offer_expiration_time: string | null
          original_offer_price: number | null
          pest_control_providers: string | null
          private_remarks: string | null
          property_id: number
          purchase_price: number | null
          sale_of_property_contingency_date: string | null
          seller_client_id: number | null
          seller_financing: boolean | null
          selling_agent_id: number | null
          selling_broker_id: number | null
          status: string | null
          title_company_id: number | null
          title_contingency_date: string | null
          total_amount_financed: number | null
          transaction_coordinators: string | null
          transaction_type: string
          updated_at: string | null
        }
        Insert: {
          acceptance_date?: string | null
          appraisal_company_id?: number | null
          appraisal_contingency_date?: string | null
          as_is_sale?: boolean | null
          buyer_client_id?: number | null
          closing_date?: string | null
          contract_date?: string | null
          created_at?: string | null
          deposit_1st_increase?: number | null
          deposit_2nd_increase?: number | null
          deposit_3rd_increase?: number | null
          deposit_amount?: number | null
          disclosure_providers?: string | null
          down_payment_amount?: number | null
          down_payment_percentage?: number | null
          earnest_money_amount?: number | null
          escrow_company_id?: number | null
          final_sale_price?: number | null
          financing_contingency_date?: string | null
          hoa_approval_contingency_date?: string | null
          hoa_providers?: string | null
          home_warranty?: boolean | null
          home_warranty_providers?: string | null
          homeowners_insurance_contingency_date?: string | null
          id?: number
          inspection_contingency_date?: string | null
          interest_rate?: number | null
          lender_id?: number | null
          listing_agent_id?: number | null
          listing_broker_id?: number | null
          loan_amount?: number | null
          loan_term_years?: number | null
          notes?: string | null
          offer_date?: string | null
          offer_expiration_date?: string | null
          offer_expiration_time?: string | null
          original_offer_price?: number | null
          pest_control_providers?: string | null
          private_remarks?: string | null
          property_id: number
          purchase_price?: number | null
          sale_of_property_contingency_date?: string | null
          seller_client_id?: number | null
          seller_financing?: boolean | null
          selling_agent_id?: number | null
          selling_broker_id?: number | null
          status?: string | null
          title_company_id?: number | null
          title_contingency_date?: string | null
          total_amount_financed?: number | null
          transaction_coordinators?: string | null
          transaction_type: string
          updated_at?: string | null
        }
        Update: {
          acceptance_date?: string | null
          appraisal_company_id?: number | null
          appraisal_contingency_date?: string | null
          as_is_sale?: boolean | null
          buyer_client_id?: number | null
          closing_date?: string | null
          contract_date?: string | null
          created_at?: string | null
          deposit_1st_increase?: number | null
          deposit_2nd_increase?: number | null
          deposit_3rd_increase?: number | null
          deposit_amount?: number | null
          disclosure_providers?: string | null
          down_payment_amount?: number | null
          down_payment_percentage?: number | null
          earnest_money_amount?: number | null
          escrow_company_id?: number | null
          final_sale_price?: number | null
          financing_contingency_date?: string | null
          hoa_approval_contingency_date?: string | null
          hoa_providers?: string | null
          home_warranty?: boolean | null
          home_warranty_providers?: string | null
          homeowners_insurance_contingency_date?: string | null
          id?: number
          inspection_contingency_date?: string | null
          interest_rate?: number | null
          lender_id?: number | null
          listing_agent_id?: number | null
          listing_broker_id?: number | null
          loan_amount?: number | null
          loan_term_years?: number | null
          notes?: string | null
          offer_date?: string | null
          offer_expiration_date?: string | null
          offer_expiration_time?: string | null
          original_offer_price?: number | null
          pest_control_providers?: string | null
          private_remarks?: string | null
          property_id?: number
          purchase_price?: number | null
          sale_of_property_contingency_date?: string | null
          seller_client_id?: number | null
          seller_financing?: boolean | null
          selling_agent_id?: number | null
          selling_broker_id?: number | null
          status?: string | null
          title_company_id?: number | null
          title_contingency_date?: string | null
          total_amount_financed?: number | null
          transaction_coordinators?: string | null
          transaction_type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transactions_buyer_client_id_fkey"
            columns: ["buyer_client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties_backup"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_seller_client_id_fkey"
            columns: ["seller_client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
