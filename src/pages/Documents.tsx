
import React from "react";
import { LuxuryLayout } from "@/components/LuxuryLayout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { FileText, FileImage, FileAudio, FileVideo, File } from "lucide-react";

const mockDocuments = [
  {
    type: "pdf",
    name: "Agreement.pdf",
    size: "1.2 MB",
    uploaded: "2 days ago",
    icon: FileText,
    description: "Signed purchase agreement"
  },
  {
    type: "image",
    name: "Condo_Photo.jpg",
    size: "680 KB",
    uploaded: "1 week ago",
    icon: FileImage,
    description: "Front view"
  },
  {
    type: "audio",
    name: "Voicemail.mp3",
    size: "320 KB",
    uploaded: "4 days ago",
    icon: FileAudio,
    description: "Client voicemail"
  },
  {
    type: "video",
    name: "VirtualTour.mp4",
    size: "18 MB",
    uploaded: "5 days ago",
    icon: FileVideo,
    description: "Virtual property tour"
  },
  {
    type: "misc",
    name: "FloorPlan.docx",
    size: "220 KB",
    uploaded: "1 month ago",
    icon: File,
    description: "Unit floor plan"
  }
];

const Documents = () => {
  return (
    <LuxuryLayout>
      <div className="p-6 max-w-6xl mx-auto">
        <h1 className="page-title">Documents</h1>
        <p className="mb-4 text-muted-foreground">
          Your documents will be displayed here.
        </p>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 animate-fade-in">
          {mockDocuments.map((doc) => (
            <Card
              key={doc.name}
              className="shadow hover:shadow-lg transition-shadow duration-300 group cursor-pointer bg-white/80 h-full flex flex-col"
            >
              <CardHeader className="flex flex-col items-center gap-2 p-5 pb-2">
                <span
                  className={
                    "rounded-xl p-3 mb-1 bg-gradient-to-br " +
                    (doc.type === "pdf"
                      ? "from-red-400 to-red-600"
                      : doc.type === "image"
                      ? "from-purple-400 to-blue-400"
                      : doc.type === "audio"
                      ? "from-green-300 to-teal-500"
                      : doc.type === "video"
                      ? "from-orange-400 to-pink-500"
                      : "from-gray-300 to-gray-400") +
                    " group-hover:scale-105 transition-transform"
                  }
                >
                  <doc.icon className="h-7 w-7 text-white" />
                </span>
                <CardTitle className="text-base text-center mt-1 break-words max-w-[90%] mx-auto">{doc.name}</CardTitle>
                <CardDescription className="text-xs text-center max-w-[90%] mx-auto">
                  {doc.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-row items-center justify-between text-xs text-muted-foreground pt-0 px-6 pb-4 mt-auto">
                <span>{doc.size}</span>
                <span>{doc.uploaded}</span>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </LuxuryLayout>
  );
};

export default Documents;
