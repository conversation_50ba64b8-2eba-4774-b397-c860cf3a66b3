import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Loader2, Brain } from 'lucide-react';
import { aiService } from '@/services/aiService';

export default function ThinkingModelTest() {
  const [query, setQuery] = useState('');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);
  const [responseTime, setResponseTime] = useState(0);

  const testQueries = [
    { query: "harris", description: "Test: Search by last name" },
    { query: "jennings", description: "Test: Non-existent name" },
    { query: "bass property", description: "Test: Property search" },
    { query: "show me aaron mcdaniel", description: "Test: Case variations" },
    { query: "I need properties under 500k with 3 bedrooms", description: "Test: Complex search" }
  ];

  const handleTest = async (testQuery: string) => {
    setQuery(testQuery);
    setLoading(true);
    setResponse('');
    
    const startTime = Date.now();
    
    try {
      const result = await aiService.sendMessage(testQuery, []);
      const elapsed = Date.now() - startTime;
      setResponseTime(elapsed);
      setResponse(result.message);
    } catch (error) {
      setResponse(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-purple-600" />
            Gemini 2.0 Flash Thinking Model Test
          </CardTitle>
          <CardDescription>
            Testing the new thinking model (gemini-2.0-flash-thinking-exp) with step-by-step reasoning
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Enter your query..."
              className="flex-1"
              onKeyPress={(e) => e.key === 'Enter' && handleTest(query)}
            />
            <Button 
              onClick={() => handleTest(query)}
              disabled={loading || !query}
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Test'}
            </Button>
          </div>

          <div className="space-y-2">
            <h3 className="text-sm font-semibold">Quick Tests:</h3>
            <div className="flex flex-wrap gap-2">
              {testQueries.map((test, idx) => (
                <Button
                  key={idx}
                  variant="outline"
                  size="sm"
                  onClick={() => handleTest(test.query)}
                  disabled={loading}
                >
                  {test.description}
                </Button>
              ))}
            </div>
          </div>

          {loading && (
            <div className="flex items-center gap-2 text-purple-600">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Thinking model is processing...</span>
            </div>
          )}

          {response && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Response Time:</span>
                <span className="font-mono">{responseTime}ms</span>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="whitespace-pre-wrap text-sm">{response}</pre>
              </div>
            </div>
          )}

          <div className="mt-6 p-4 bg-purple-50 rounded-lg">
            <h4 className="font-semibold text-purple-900 mb-2">Model Configuration:</h4>
            <ul className="text-sm space-y-1 text-purple-800">
              <li>• Model: <code className="bg-purple-100 px-1 rounded">gemini-2.0-flash-thinking-exp</code></li>
              <li>• Temperature: 0.7 (more creative)</li>
              <li>• Max Tokens: 8192 (for longer reasoning)</li>
              <li>• Personality: Warm, conversational, thoughtful</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}