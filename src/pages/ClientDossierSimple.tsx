import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { LuxuryLayout } from '@/components/LuxuryLayout';
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

const ClientDossierSimple = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  return (
    <LuxuryLayout>
      <div className="p-6">
        <Button 
          onClick={() => navigate("/clients")} 
          variant="ghost" 
          className="mb-4 hover:bg-white/20"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Clients
        </Button>
        
        <h1 className="text-3xl font-bold text-slate-800 mb-4">
          Client Dossier - ID: {id}
        </h1>
        
        <p className="text-slate-600">
          Simple version to test if the route is working.
        </p>
      </div>
    </LuxuryLayout>
  );
};

export default ClientDossierSimple;