import React, { useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { LuxuryLayout } from '@/components/LuxuryLayout';
import { getMockClients, getMockProperties } from "@/lib/mockData";
import { getTopMatchesForClient } from "@/lib/propertyMatcher";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/hooks/use-toast";
import { 
  ArrowLeft, 
  Mail, 
  Phone, 
  MapPin, 
  DollarSign, 
  Home, 
  Calendar,
  Star,
  Clock,
  FileText,
  TrendingUp,
  <PERSON>rkles,
  Save,
  X,
  Bed,
  Bath
} from "lucide-react";

const ClientDossier = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [editingNotes, setEditingNotes] = useState(false);
  const [notes, setNotes] = useState("");
  const [showMatches, setShowMatches] = useState(false);
  
  const client = getMockClients().find(c => c.id === parseInt(id || "0"));
  const properties = getMockProperties();
  const propertyMatches = client ? getTopMatchesForClient(client, properties) : [];
  
  // Initialize notes from localStorage or client data
  React.useEffect(() => {
    if (client?.id) {
      // Try to load from localStorage first
      const savedNotes = localStorage.getItem(`client_notes_${client.id}`);
      if (savedNotes !== null) {
        setNotes(savedNotes);
      } else if (client.notes) {
        // Fall back to mock data if no saved notes
        setNotes(client.notes);
      }
    }
  }, [client?.id]);

  // Save notes to localStorage
  const saveNotes = () => {
    if (client?.id) {
      try {
        localStorage.setItem(`client_notes_${client.id}`, notes);
        setEditingNotes(false);
        toast({
          title: "Notes saved",
          description: "Your notes have been saved successfully.",
        });
      } catch (error) {
        toast({
          title: "Error saving notes",
          description: "Failed to save notes. Please try again.",
          variant: "destructive",
        });
      }
    }
  };
  
  if (!client) {
    return (
      <LuxuryLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-slate-800 mb-4">Client Not Found</h2>
            <Button onClick={() => navigate("/clients")} variant="outline">
              Return to Clients
            </Button>
          </div>
        </div>
      </LuxuryLayout>
    );
  }

  const formatCurrency = (value: string | number | undefined) => {
    if (!value) return "N/A";
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0 }).format(num);
  };

  const formatDate = (dateString: string | undefined | null) => {
    if (!dateString) return "Never";
    return new Date(dateString).toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };


  return (
    <LuxuryLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Button 
            onClick={() => navigate("/clients")} 
            variant="ghost" 
            className="mb-4 hover:bg-white/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Clients
          </Button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-slate-800 mb-2">
                {client.first_name} {client.last_name}
              </h1>
              <div className="flex items-center gap-4 text-slate-600">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  client.client_type === 'buyer' ? 'bg-blue-100 text-blue-800' :
                  client.client_type === 'seller' ? 'bg-green-100 text-green-800' :
                  client.client_type === 'investor' ? 'bg-purple-100 text-purple-800' :
                  'bg-orange-100 text-orange-800'
                }`}>
                  {client.client_type}
                </span>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  client.status === 'active' ? 'bg-emerald-100 text-emerald-800' :
                  client.status === 'qualified' ? 'bg-cyan-100 text-cyan-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {client.status}
                </span>
                <span className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-500 mr-1" />
                  {'⭐'.repeat(client.rating || 0)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Contact Information */}
          <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <Mail className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-slate-600">Email</p>
                <p className="font-medium text-slate-800">{client.email || "Not provided"}</p>
              </div>
              <div>
                <p className="text-sm text-slate-600">Phone</p>
                <p className="font-medium text-slate-800">{client.mobile_phone || "Not provided"}</p>
              </div>
              <div>
                <p className="text-sm text-slate-600">Address</p>
                <p className="font-medium text-slate-800">
                  {client.street_address || "Not provided"}<br />
                  {client.city && client.state && client.zip_code ? 
                    `${client.city}, ${client.state} ${client.zip_code}` : 
                    ""}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Financial Profile */}
          <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <DollarSign className="h-5 w-5" />
                Financial Profile
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-slate-600">Price Range</p>
                <p className="font-medium text-slate-800">
                  {formatCurrency(client.price_range_min)} - {formatCurrency(client.price_range_max)}
                </p>
              </div>
              <div>
                <p className="text-sm text-slate-600">Pre-Approval Amount</p>
                <p className="font-medium text-slate-800">{formatCurrency(client.pre_approval_amount)}</p>
              </div>
              <div>
                <p className="text-sm text-slate-600">Lender</p>
                <p className="font-medium text-slate-800">{client.lender_name || "Not specified"}</p>
              </div>
            </CardContent>
          </Card>

          {/* Property Preferences */}
          <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <Home className="h-5 w-5" />
                Property Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-slate-600">Property Type</p>
                <p className="font-medium text-slate-800">{client.property_type_preference || "Any"}</p>
              </div>
              <div>
                <p className="text-sm text-slate-600">Minimum Bedrooms</p>
                <p className="font-medium text-slate-800">{client.min_bedrooms || "Not specified"}</p>
              </div>
              <div>
                <p className="text-sm text-slate-600">Location Preference</p>
                <p className="font-medium text-slate-800">{client.city || "Flexible"}</p>
              </div>
            </CardContent>
          </Card>

          {/* Timeline & Motivation */}
          <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <Calendar className="h-5 w-5" />
                Timeline & Motivation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-slate-600">Timeline to Buy</p>
                <p className="font-medium text-slate-800">{client.timeline_to_buy || "Flexible"}</p>
              </div>
              <div>
                <p className="text-sm text-slate-600">Motivation Level</p>
                <p className="font-medium text-slate-800">{client.motivation_level || "Unknown"}</p>
              </div>
              <div>
                <p className="text-sm text-slate-600">Priority</p>
                <p className={`font-medium ${
                  client.priority === 'high' ? 'text-red-600' : 
                  client.priority === 'normal' ? 'text-yellow-600' : 
                  'text-slate-600'
                }`}>
                  {client.priority?.toUpperCase() || "NORMAL"}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Engagement Status */}
          <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <TrendingUp className="h-5 w-5" />
                Engagement Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-slate-600">Current Stage</p>
                <p className="font-medium text-slate-800 capitalize">{client.stage || "Unknown"}</p>
              </div>
              <div>
                <p className="text-sm text-slate-600">Last Contact</p>
                <p className="font-medium text-slate-800">{formatDate(client.last_contact_date)}</p>
              </div>
              <div>
                <p className="text-sm text-slate-600">Client Since</p>
                <p className="font-medium text-slate-800">{formatDate(client.created_at)}</p>
              </div>
            </CardContent>
          </Card>

          {/* Activity Timeline */}
          <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <Clock className="h-5 w-5" />
                Activity Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5"></div>
                  <div>
                    <p className="text-sm font-medium text-slate-800">Client Added</p>
                    <p className="text-xs text-slate-600">{formatDate(client.created_at)}</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-1.5"></div>
                  <div>
                    <p className="text-sm font-medium text-slate-800">Last Updated</p>
                    <p className="text-xs text-slate-600">{formatDate(client.updated_at)}</p>
                  </div>
                </div>
                {client.last_contact_date && (
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-1.5"></div>
                    <div>
                      <p className="text-sm font-medium text-slate-800">Last Contact</p>
                      <p className="text-xs text-slate-600">{formatDate(client.last_contact_date)}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Notes Section */}
        <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl mt-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between text-slate-800">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Notes
              </div>
              {editingNotes ? (
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={saveNotes}
                    className="hover:bg-white/20"
                  >
                    <Save className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      setNotes(client.notes || "");
                      setEditingNotes(false);
                    }}
                    className="hover:bg-white/20"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setEditingNotes(true)}
                  className="hover:bg-white/20"
                >
                  Edit
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {editingNotes ? (
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="min-h-[100px] bg-white/50 border-white/30"
                placeholder="Add notes about this client..."
              />
            ) : (
              <p className="text-slate-700 leading-relaxed">
                {notes || "No notes yet. Click Edit to add notes."}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Property Matches Section */}
        {showMatches && propertyMatches.length > 0 && (
          <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl mt-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-slate-800">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  AI-Matched Properties
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowMatches(false)}
                  className="hover:bg-white/20"
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {propertyMatches.map((match, index) => (
                  <div 
                    key={match.property.id} 
                    className="border rounded-lg p-4 bg-white/50 hover:bg-white/60 transition-all cursor-pointer"
                    onClick={() => navigate(`/properties/${match.property.id}`)}
                  >
                    <div className="flex items-start gap-4">
                      <div className="relative w-32 h-24 rounded-lg overflow-hidden flex-shrink-0">
                        <img
                          src={match.property.photo_url}
                          alt={match.property.address_street_complete}
                          className="object-cover w-full h-full"
                          onError={(e) => {
                            e.currentTarget.src = '/placeholder.svg';
                          }}
                        />
                        <div className="absolute top-2 left-2">
                          <Badge variant="secondary" className="bg-cyan-500 text-white">
                            {match.matchScore}% Match
                          </Badge>
                        </div>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-slate-800">
                          {match.property.address_street_complete}
                        </h4>
                        <p className="text-sm text-slate-600">
                          {match.property.address_city}, CA {match.property.address_zip_code}
                        </p>
                        <p className="text-lg font-semibold text-slate-800 mt-1">
                          {formatCurrency(match.property.list_price)}
                        </p>
                        <div className="flex items-center gap-4 mt-2 text-sm text-slate-600">
                          <span className="flex items-center gap-1">
                            <Bed className="h-4 w-4" />
                            {match.property.bedrooms_and_possible_bedrooms} beds
                          </span>
                          <span className="flex items-center gap-1">
                            <Bath className="h-4 w-4" />
                            {match.property.full_bathrooms} baths
                          </span>
                          <span className="flex items-center gap-1">
                            <Home className="h-4 w-4" />
                            {match.property.square_footage.toLocaleString()} sqft
                          </span>
                        </div>
                        <div className="mt-2">
                          <Progress value={match.matchScore} className="h-2" />
                        </div>
                        <div className="mt-2 flex flex-wrap gap-1">
                          {match.matchReasons.map((reason, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {reason}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="mt-8 flex gap-4 justify-center">
          <Button className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white shadow-lg">
            <Mail className="h-4 w-4 mr-2" />
            Send Email
          </Button>
          <Button className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg">
            <Phone className="h-4 w-4 mr-2" />
            Call Client
          </Button>
          <Button 
            onClick={() => setShowMatches(!showMatches)}
            className="bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white shadow-lg"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            {showMatches ? 'Hide Matches' : 'Match to Properties'}
          </Button>
        </div>

      </div>
    </LuxuryLayout>
  );
};

export default ClientDossier;