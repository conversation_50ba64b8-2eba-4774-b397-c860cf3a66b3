import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Home, Bed, Bath, Car, Trees, Filter, X, DollarSign, Square } from "lucide-react";
import { getMockProperties } from "@/lib/mockData";
import { LuxuryLayout } from "@/components/LuxuryLayout";

export default function Properties() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("price-low");
  const [filterCity, setFilterCity] = useState("all");
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  
  // Advanced filters
  const [priceRange, setPriceRange] = useState([0, 5000000]);
  const [minBedrooms, setMinBedrooms] = useState(0);
  const [minBathrooms, setMinBathrooms] = useState(0);
  const [minSquareFeet, setMinSquareFeet] = useState(0);
  const [propertyTypes, setPropertyTypes] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState("all");
  
  const properties = getMockProperties();
  
  // Get unique values for filters
  const cities = ["all", ...new Set(properties.map(p => p.address_city))];
  const uniquePropertyTypes = [...new Set(properties.map(p => p.property_type).filter(Boolean))];
  const maxPrice = Math.max(...properties.map(p => parseFloat(p.list_price)));
  const minPrice = Math.min(...properties.map(p => parseFloat(p.list_price)));
  
  // Filter and sort properties
  let filteredProperties = properties.filter(property => {
    const matchesSearch = 
      property.address_street_complete.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.address_city.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.subdivision?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.architectural_style?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCity = filterCity === "all" || property.address_city === filterCity;
    
    // Advanced filters
    const price = parseFloat(property.list_price);
    const matchesPrice = price >= priceRange[0] && price <= priceRange[1];
    const matchesBedrooms = property.bedrooms_and_possible_bedrooms >= minBedrooms;
    const matchesBathrooms = parseFloat(property.full_bathrooms) >= minBathrooms;
    const matchesSquareFeet = property.square_footage >= minSquareFeet;
    const matchesPropertyType = propertyTypes.length === 0 || propertyTypes.includes(property.property_type);
    const matchesStatus = statusFilter === "all" || property.status === statusFilter;
    
    return matchesSearch && matchesCity && matchesPrice && matchesBedrooms && 
           matchesBathrooms && matchesSquareFeet && matchesPropertyType && matchesStatus;
  });
  
  // Sort properties
  filteredProperties.sort((a, b) => {
    const priceA = parseFloat(a.list_price);
    const priceB = parseFloat(b.list_price);
    
    switch (sortBy) {
      case "price-low":
        return priceA - priceB;
      case "price-high":
        return priceB - priceA;
      case "newest":
        return b.dom - a.dom;
      case "size":
        return b.square_footage - a.square_footage;
      default:
        return 0;
    }
  });
  
  const formatPrice = (price: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(parseFloat(price));
  };
  
  return (
    <LuxuryLayout>
      <div className="space-y-8 animate-fade-in">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Properties</h2>
          <p className="text-muted-foreground">
            Browse {properties.length} available properties
          </p>
        </div>
      
      <div className="flex flex-col sm:flex-row gap-4">
        <Input
          placeholder="Search by address, city, subdivision, or style..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1"
        />
        
        <Select value={filterCity} onValueChange={setFilterCity}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by city" />
          </SelectTrigger>
          <SelectContent>
            {cities.map(city => (
              <SelectItem key={city} value={city}>
                {city === "all" ? "All Cities" : city}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="price-low">Price: Low to High</SelectItem>
            <SelectItem value="price-high">Price: High to Low</SelectItem>
            <SelectItem value="newest">Newest First</SelectItem>
            <SelectItem value="size">Largest First</SelectItem>
          </SelectContent>
        </Select>
        
        <Sheet open={showAdvancedFilters} onOpenChange={setShowAdvancedFilters}>
          <SheetTrigger asChild>
            <Button variant="outline" className="w-full sm:w-auto">
              <Filter className="mr-2 h-4 w-4" />
              Advanced Filters
            </Button>
          </SheetTrigger>
          <SheetContent className="w-full sm:max-w-md">
            <SheetHeader>
              <SheetTitle>Advanced Filters</SheetTitle>
              <SheetDescription>
                Refine your property search with detailed criteria
              </SheetDescription>
            </SheetHeader>
            
            <div className="space-y-6 mt-6">
              {/* Price Range */}
              <div className="space-y-2">
                <Label>Price Range</Label>
                <div className="px-2">
                  <Slider
                    value={priceRange}
                    onValueChange={setPriceRange}
                    min={0}
                    max={5000000}
                    step={50000}
                    className="mb-2"
                  />
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>{formatPrice(priceRange[0].toString())}</span>
                    <span>{formatPrice(priceRange[1].toString())}</span>
                  </div>
                </div>
              </div>
              
              {/* Bedrooms */}
              <div className="space-y-2">
                <Label>Minimum Bedrooms</Label>
                <Select value={minBedrooms.toString()} onValueChange={(val) => setMinBedrooms(parseInt(val))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Any</SelectItem>
                    <SelectItem value="1">1+</SelectItem>
                    <SelectItem value="2">2+</SelectItem>
                    <SelectItem value="3">3+</SelectItem>
                    <SelectItem value="4">4+</SelectItem>
                    <SelectItem value="5">5+</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Bathrooms */}
              <div className="space-y-2">
                <Label>Minimum Bathrooms</Label>
                <Select value={minBathrooms.toString()} onValueChange={(val) => setMinBathrooms(parseInt(val))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Any</SelectItem>
                    <SelectItem value="1">1+</SelectItem>
                    <SelectItem value="2">2+</SelectItem>
                    <SelectItem value="3">3+</SelectItem>
                    <SelectItem value="4">4+</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Square Footage */}
              <div className="space-y-2">
                <Label>Minimum Square Feet</Label>
                <Select value={minSquareFeet.toString()} onValueChange={(val) => setMinSquareFeet(parseInt(val))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Any</SelectItem>
                    <SelectItem value="1000">1,000+</SelectItem>
                    <SelectItem value="1500">1,500+</SelectItem>
                    <SelectItem value="2000">2,000+</SelectItem>
                    <SelectItem value="2500">2,500+</SelectItem>
                    <SelectItem value="3000">3,000+</SelectItem>
                    <SelectItem value="4000">4,000+</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Property Type */}
              <div className="space-y-2">
                <Label>Property Type</Label>
                <div className="space-y-2">
                  {uniquePropertyTypes.map(type => (
                    <label key={type} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={propertyTypes.includes(type)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setPropertyTypes([...propertyTypes, type]);
                          } else {
                            setPropertyTypes(propertyTypes.filter(t => t !== type));
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm">{type}</span>
                    </label>
                  ))}
                </div>
              </div>
              
              {/* Status */}
              <div className="space-y-2">
                <Label>Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="Active">Active</SelectItem>
                    <SelectItem value="Pending">Pending</SelectItem>
                    <SelectItem value="Sold">Sold</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Clear Filters */}
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => {
                  setPriceRange([0, 5000000]);
                  setMinBedrooms(0);
                  setMinBathrooms(0);
                  setMinSquareFeet(0);
                  setPropertyTypes([]);
                  setStatusFilter("all");
                }}
              >
                <X className="mr-2 h-4 w-4" />
                Clear All Filters
              </Button>
            </div>
          </SheetContent>
        </Sheet>
      </div>
      
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {filteredProperties.length} properties
        </div>
        {(minBedrooms > 0 || minBathrooms > 0 || minSquareFeet > 0 || propertyTypes.length > 0 || statusFilter !== "all" || priceRange[0] > 0 || priceRange[1] < 5000000) && (
          <div className="flex flex-wrap gap-2">
            {(priceRange[0] > 0 || priceRange[1] < 5000000) && (
              <Badge variant="secondary">
                Price: {formatPrice(priceRange[0].toString())} - {formatPrice(priceRange[1].toString())}
              </Badge>
            )}
            {minBedrooms > 0 && (
              <Badge variant="secondary">{minBedrooms}+ Beds</Badge>
            )}
            {minBathrooms > 0 && (
              <Badge variant="secondary">{minBathrooms}+ Baths</Badge>
            )}
            {minSquareFeet > 0 && (
              <Badge variant="secondary">{minSquareFeet.toLocaleString()}+ sqft</Badge>
            )}
            {propertyTypes.length > 0 && (
              <Badge variant="secondary">{propertyTypes.join(", ")}</Badge>
            )}
            {statusFilter !== "all" && (
              <Badge variant="secondary">Status: {statusFilter}</Badge>
            )}
          </div>
        )}
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredProperties.map((property) => (
          <Card 
            key={property.id} 
            className="overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer hover-lift animate-scale-in"
            onClick={() => navigate(`/properties/${property.id}`)}
          >
            <div className="aspect-video relative">
              <img
                src={property.photo_url}
                alt={property.address_street_complete}
                className="object-cover w-full h-full"
                onError={(e) => {
                  e.currentTarget.src = '/placeholder.svg';
                }}
              />
              <Badge className="absolute top-2 right-2" variant={property.status === 'Active' ? 'default' : 'secondary'}>
                {property.status}
              </Badge>
              {property.dom > 0 && (
                <Badge className="absolute top-2 left-2" variant="outline">
                  {property.dom} days
                </Badge>
              )}
            </div>
            
            <CardHeader>
              <CardTitle className="text-2xl">{formatPrice(property.list_price)}</CardTitle>
              <CardDescription>
                <div className="font-medium text-foreground">
                  {property.address_street_complete}
                </div>
                <div>
                  {property.address_city}, CA {property.address_zip_code}
                </div>
                {property.subdivision && (
                  <div className="text-sm mt-1">{property.subdivision}</div>
                )}
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <Bed className="w-4 h-4 text-muted-foreground" />
                  <span>{property.bedrooms_and_possible_bedrooms} beds</span>
                </div>
                <div className="flex items-center gap-2">
                  <Bath className="w-4 h-4 text-muted-foreground" />
                  <span>{property.full_bathrooms} baths</span>
                </div>
                <div className="flex items-center gap-2">
                  <Home className="w-4 h-4 text-muted-foreground" />
                  <span>{property.square_footage.toLocaleString()} sq ft</span>
                </div>
                <div className="flex items-center gap-2">
                  <Trees className="w-4 h-4 text-muted-foreground" />
                  <span>{parseFloat(property.lot_size_acres).toFixed(2)} acres</span>
                </div>
                {property.garage_spaces > 0 && (
                  <div className="flex items-center gap-2 col-span-2">
                    <Car className="w-4 h-4 text-muted-foreground" />
                    <span>{property.garage_spaces} car garage</span>
                  </div>
                )}
              </div>
              
              {property.architectural_style && (
                <div className="mt-3 flex flex-wrap gap-1">
                  {property.architectural_style.split(',').map((style, idx) => (
                    <Badge key={idx} variant="outline" className="text-xs">
                      {style.trim()}
                    </Badge>
                  ))}
                </div>
              )}
              
              <p className="mt-3 text-sm text-muted-foreground line-clamp-2">
                {property.public_remarks}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
      </div>
    </LuxuryLayout>
  );
}