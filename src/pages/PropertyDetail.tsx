import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { ArrowLeft, Bed, Bath, Home, Car, Trees, Calendar, Heart, Share2, MapPin } from "lucide-react";
import { getMockProperties } from "@/lib/mockData";
import PropertyMap from "@/components/PropertyMap";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { LuxuryLayout } from "@/components/LuxuryLayout";
import { toast } from "sonner";
import {
  TooltipProvider,
} from "@/components/ui/tooltip";

export default function PropertyDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const properties = getMockProperties();
  
  const property = properties.find(p => p.id === parseInt(id || '0'));
  
  if (!property) {
    return (
      <LuxuryLayout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <h2 className="text-2xl font-bold mb-4">Property not found</h2>
          <Button onClick={() => navigate("/properties")} variant="default">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Properties
          </Button>
        </div>
      </LuxuryLayout>
    );
  }
  
  const formatPrice = (price: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(parseFloat(price));
  };
  
  // Find similar properties (same city, price within 20%)
  const priceNum = parseFloat(property.list_price);
  const minPrice = priceNum * 0.8;
  const maxPrice = priceNum * 1.2;
  
  const similarProperties = properties
    .filter(p => 
      p.id !== property.id && 
      p.address_city === property.address_city &&
      parseFloat(p.list_price) >= minPrice &&
      parseFloat(p.list_price) <= maxPrice
    )
    .slice(0, 3);
  
  const handleScheduleShowing = () => {
    toast.success("Showing request sent! An agent will contact you soon.");
  };
  
  const handleSaveProperty = () => {
    toast.success("Property saved to your favorites!");
  };
  
  const handleShare = () => {
    toast.success("Property link copied to clipboard!");
  };
  
  return (
    <TooltipProvider>
      <LuxuryLayout>
      <div className="space-y-6">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <button onClick={() => navigate("/properties")} className="hover:text-foreground">
            Properties
          </button>
          <span>/</span>
          <span className="text-foreground">{property.address_street_complete}</span>
        </div>
        
        {/* Hero Section */}
        <div className="grid lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="relative rounded-lg overflow-hidden">
              <img
                src={property.photo_url}
                alt={property.address_street_complete}
                className="w-full h-[500px] object-cover"
                onError={(e) => {
                  e.currentTarget.src = '/placeholder.svg';
                }}
              />
              <Badge className="absolute top-4 right-4" variant={property.status === 'Active' ? 'default' : 'secondary'}>
                {property.status}
              </Badge>
            </div>
            
            {/* Thumbnail Gallery */}
            <div className="grid grid-cols-4 gap-2 mt-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="rounded overflow-hidden cursor-pointer hover:opacity-80 transition">
                  <img
                    src={`/placeholder.svg`}
                    alt={`View ${i}`}
                    className="w-full h-24 object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
          
          {/* Price and Actions */}
          <div className="space-y-4">
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-3xl">{formatPrice(property.list_price)}</CardTitle>
                <p className="text-muted-foreground">{property.address_street_complete}</p>
                <p className="text-muted-foreground">{property.address_city}, CA {property.address_zip_code}</p>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" size="lg" onClick={handleScheduleShowing}>
                  <Calendar className="mr-2 h-4 w-4" /> Schedule Showing
                </Button>
                <div className="grid grid-cols-2 gap-2">
                  <Button variant="outline" onClick={handleSaveProperty}>
                    <Heart className="mr-2 h-4 w-4" /> Save
                  </Button>
                  <Button variant="outline" onClick={handleShare}>
                    <Share2 className="mr-2 h-4 w-4" /> Share
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            {/* Key Features */}
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Bed className="h-4 w-4 text-muted-foreground" />
                      <span>Bedrooms</span>
                    </div>
                    <span className="font-semibold">{property.bedrooms_and_possible_bedrooms}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Bath className="h-4 w-4 text-muted-foreground" />
                      <span>Bathrooms</span>
                    </div>
                    <span className="font-semibold">{property.full_bathrooms}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Home className="h-4 w-4 text-muted-foreground" />
                      <span>Square Feet</span>
                    </div>
                    <span className="font-semibold">{property.square_footage.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Trees className="h-4 w-4 text-muted-foreground" />
                      <span>Lot Size</span>
                    </div>
                    <span className="font-semibold">{parseFloat(property.lot_size_acres).toFixed(2)} acres</span>
                  </div>
                  {property.garage_spaces > 0 && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Car className="h-4 w-4 text-muted-foreground" />
                        <span>Garage</span>
                      </div>
                      <span className="font-semibold">{property.garage_spaces} cars</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Detailed Information */}
        <div className="grid lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            {/* Description */}
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Property Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">{property.public_remarks}</p>
              </CardContent>
            </Card>
            
            {/* Property Details */}
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Property Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2">General</h4>
                    <dl className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <dt className="text-muted-foreground">Type:</dt>
                        <dd>{property.property_type}</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-muted-foreground">Style:</dt>
                        <dd>{property.architectural_style || 'N/A'}</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-muted-foreground">Year Built:</dt>
                        <dd>{property.year_built}</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-muted-foreground">Days on Market:</dt>
                        <dd>{property.dom}</dd>
                      </div>
                    </dl>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Location</h4>
                    <dl className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <dt className="text-muted-foreground">Subdivision:</dt>
                        <dd>{property.subdivision || 'N/A'}</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-muted-foreground">County:</dt>
                        <dd>{property.county || 'N/A'}</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-muted-foreground">MLS #:</dt>
                        <dd>{property.listing_number}</dd>
                      </div>
                    </dl>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Map */}
          <div>
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" /> Location
                </CardTitle>
              </CardHeader>
              <CardContent>
                <PropertyMap
                  address_street_complete={property.address_street_complete}
                  address_city={property.address_city}
                  address_zip_code={property.address_zip_code}
                  // latitude and longitude are not available in mock data,
                  // PropertyMap will handle geocoding.
                />
                <p className="mt-3 text-sm text-muted-foreground">
                  {property.address_street_complete}<br />
                  {property.address_city}, CA {property.address_zip_code}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Similar Properties */}
        {similarProperties.length > 0 && (
          <div>
            <h3 className="text-2xl font-bold mb-4">Similar Properties</h3>
            <div className="grid md:grid-cols-3 gap-4">
              {similarProperties.map((similar) => (
                <Card 
                  key={similar.id} 
                  className="cursor-pointer hover:shadow-lg transition-shadow"
                  onClick={() => navigate(`/properties/${similar.id}`)}
                >
                  <div className="aspect-video relative">
                    <img
                      src={similar.photo_url}
                      alt={similar.address_street_complete}
                      className="object-cover w-full h-full rounded-t-lg"
                      onError={(e) => {
                        e.currentTarget.src = '/placeholder.svg';
                      }}
                    />
                  </div>
                  <CardContent className="pt-4">
                    <p className="font-semibold text-lg">{formatPrice(similar.list_price)}</p>
                    <p className="text-sm text-muted-foreground">{similar.address_street_complete}</p>
                    <div className="flex gap-4 mt-2 text-sm text-muted-foreground">
                      <span>{similar.bedrooms_and_possible_bedrooms} beds</span>
                      <span>{similar.full_bathrooms} baths</span>
                      <span>{similar.square_footage.toLocaleString()} sqft</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
      </LuxuryLayout>
    </TooltipProvider>
  );
}