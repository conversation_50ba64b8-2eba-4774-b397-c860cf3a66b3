import React from 'react';
import { LuxuryLayout } from '@/components/LuxuryLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { devLogItems, getDevLogStats, type DevLogItem } from '@/lib/devLogData';
import { useNavigate } from 'react-router-dom';
import { 
  Bug, 
  Sparkles, 
  Wrench, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Calendar,
  MapPin,
  TrendingUp,
  FileCode,
  ChevronDown
} from 'lucide-react';

export default function DevLog() {
  const navigate = useNavigate();
  const stats = getDevLogStats();

  const getStatusIcon = (status: DevLogItem['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'in-progress':
        return <AlertCircle className="h-4 w-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  const getTypeIcon = (type: DevLogItem['type']) => {
    switch (type) {
      case 'bug':
        return <Bug className="h-4 w-4" />;
      case 'feature':
        return <Sparkles className="h-4 w-4" />;
      case 'enhancement':
        return <Wrench className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: DevLogItem['priority']) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
    }
  };

  const navigateToComponent = (item: DevLogItem) => {
    // For now, navigate to Clients page if it's ClientDossier
    if (item.component.includes('ClientDossier')) {
      navigate('/clients');
    }
  };

  const pendingItems = devLogItems.filter(item => item.status === 'pending');
  const completedItems = devLogItems.filter(item => item.status === 'completed');

  return (
    <LuxuryLayout>
      <div className="min-h-screen relative">
        {/* Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-100" />
        
        <div className="relative z-10 p-6 max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-slate-800 mb-2 flex items-center gap-3">
              <FileCode className="h-10 w-10 text-blue-600" />
              Development Log
            </h1>
            <p className="text-slate-600 text-lg">
              Track bugs, features, and improvements in real-time
            </p>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-slate-600">Total Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-800">{stats.total}</div>
              </CardContent>
            </Card>
            <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-yellow-600">Pending</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
              </CardContent>
            </Card>
            <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-green-600">Completed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
              </CardContent>
            </Card>
            <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-blue-600">Completion Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats.completionRate}%</div>
                <Progress value={stats.completionRate} className="mt-2" />
              </CardContent>
            </Card>
          </div>

          {/* Completed Items - Compact Changelog Style */}
          {completedItems.length > 0 && (
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-slate-800 mb-6 flex items-center gap-2">
                <CheckCircle className="h-6 w-6 text-green-500" />
                Recent Changes
              </h2>
              <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
                <CardContent className="p-6">
                  <div className="space-y-1">
                    {completedItems.map((item, index) => (
                      <details key={item.id} className="group">
                        <summary className="flex items-center justify-between cursor-pointer p-3 -mx-3 rounded-lg hover:bg-slate-50/50 transition-colors list-none">
                          <div className="flex items-center gap-3 flex-1 min-w-0">
                            <div className="flex items-center gap-2 flex-shrink-0">
                              {getTypeIcon(item.type)}
                              <Badge variant="outline" className={`text-xs ${
                                item.type === 'bug' ? 'border-red-300 text-red-700 bg-red-50' :
                                item.type === 'feature' ? 'border-blue-300 text-blue-700 bg-blue-50' :
                                'border-purple-300 text-purple-700 bg-purple-50'
                              }`}>
                                {item.type}
                              </Badge>
                            </div>
                            <h3 className="font-medium text-slate-800 truncate">{item.title}</h3>
                            <span className="text-sm text-slate-500 hidden sm:inline">• {item.component}</span>
                          </div>
                          <div className="flex items-center gap-3 flex-shrink-0">
                            <span className="text-sm text-slate-500">{item.dateCompleted}</span>
                            <ChevronDown className="h-4 w-4 text-slate-400 group-open:rotate-180 transition-transform" />
                          </div>
                        </summary>
                        <div className="mt-3 pl-12 pr-3 space-y-2 pb-3 border-b border-slate-100 last:border-0">
                          <p className="text-sm text-slate-600">{item.description}</p>
                          {item.result && (
                            <div className="p-2 bg-green-50 rounded-md border border-green-200">
                              <p className="text-sm text-green-700">✓ {item.result}</p>
                            </div>
                          )}
                          {item.workLog && (
                            <p className="text-sm text-slate-500"><span className="font-medium">Implementation:</span> {item.workLog}</p>
                          )}
                          {item.location && (
                            <p className="text-sm text-slate-400">Location: {item.location}</p>
                          )}
                        </div>
                      </details>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Pending Items - Compact Style */}
          {pendingItems.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-slate-800 mb-6 flex items-center gap-2">
                <AlertCircle className="h-6 w-6 text-orange-500" />
                Known Issues
              </h2>
              <Card className="bg-white/40 backdrop-blur-xl border-white/30 shadow-xl">
                <CardContent className="p-6">
                  <div className="space-y-2">
                    {pendingItems.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center justify-between p-3 -mx-3 rounded-lg hover:bg-orange-50/30 transition-colors cursor-pointer"
                        onClick={() => {
                          if (item.component === 'ClientDossier.tsx') {
                            navigate('/client/1');
                          }
                        }}
                      >
                        <div className="flex items-center gap-3 flex-1">
                          <div className="flex items-center gap-2">
                            {getTypeIcon(item.type)}
                            <Badge className={`${getPriorityColor(item.priority)} text-xs`}>
                              {item.priority}
                            </Badge>
                          </div>
                          <h3 className="font-medium text-slate-800">{item.title}</h3>
                          <span className="text-sm text-slate-500 hidden sm:inline">• {item.component}</span>
                        </div>
                        <span className="text-sm text-slate-500">{item.dateIdentified}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Development Note */}
          <Card className="mt-8 bg-blue-50/40 backdrop-blur-xl border-blue-200/30">
            <CardHeader>
              <CardTitle className="text-blue-800">📋 About This Page</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-blue-700">
                This development log showcases the iterative improvement process of this CRM application. 
                It demonstrates how bugs are identified, tracked, and resolved, providing transparency 
                into the development workflow and problem-solving approach.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </LuxuryLayout>
  );
}