import React, { useState } from "react";
import { LuxuryLayout } from "@/components/LuxuryLayout";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { getMockClients, getMockProperties } from "@/lib/mockData";
import { Calendar as CalendarIcon, Clock, MapPin, User, Home, Plus, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { format, isSameDay, startOfDay, addDays } from "date-fns";
import { toast } from "sonner";

interface Appointment {
  id: string;
  title: string;
  type: "showing" | "meeting" | "inspection" | "closing" | "other";
  clientId?: number;
  propertyId?: string;
  date: Date;
  time: string;
  duration: number; // in minutes
  location: string;
  notes: string;
  status: "scheduled" | "completed" | "cancelled";
}

// Generate mock appointments
const generateMockAppointments = (): Appointment[] => {
  const clients = getMockClients();
  const properties = getMockProperties();
  const today = new Date();
  
  return [
    {
      id: "1",
      title: "Property Showing - Downtown Condo",
      type: "showing",
      clientId: clients[0]?.id,
      propertyId: properties[0]?.id,
      date: today,
      time: "10:00 AM",
      duration: 60,
      location: properties[0]?.address_street_complete || "123 Main St",
      notes: "Client interested in luxury condos, first showing",
      status: "scheduled"
    },
    {
      id: "2",
      title: "Client Meeting - Investment Strategy",
      type: "meeting",
      clientId: clients[1]?.id,
      date: today,
      time: "2:00 PM",
      duration: 90,
      location: "Office - Conference Room A",
      notes: "Discuss multi-property investment portfolio",
      status: "scheduled"
    },
    {
      id: "3",
      title: "Home Inspection",
      type: "inspection",
      propertyId: properties[2]?.id,
      clientId: clients[2]?.id,
      date: addDays(today, 1),
      time: "9:00 AM",
      duration: 120,
      location: properties[2]?.address_street_complete || "789 Oak Ave",
      notes: "Professional inspection for pending offer",
      status: "scheduled"
    },
    {
      id: "4",
      title: "Property Closing",
      type: "closing",
      clientId: clients[3]?.id,
      propertyId: properties[5]?.id,
      date: addDays(today, 2),
      time: "3:00 PM",
      duration: 120,
      location: "Title Company - 456 Legal Blvd",
      notes: "Final closing documents and key handover",
      status: "scheduled"
    },
    {
      id: "5",
      title: "Virtual Tour - Luxury Estate",
      type: "showing",
      clientId: clients[4]?.id,
      propertyId: properties[8]?.id,
      date: addDays(today, 3),
      time: "11:00 AM",
      duration: 45,
      location: "Virtual - Zoom Meeting",
      notes: "Out-of-state client, interested in $2M+ properties",
      status: "scheduled"
    },
    {
      id: "6",
      title: "Market Analysis Review",
      type: "meeting",
      clientId: clients[5]?.id,
      date: addDays(today, -2),
      time: "10:00 AM",
      duration: 60,
      location: "Office",
      notes: "Reviewed comparable sales and pricing strategy",
      status: "completed"
    },
    {
      id: "7",
      title: "Cancelled Showing",
      type: "showing",
      clientId: clients[6]?.id,
      propertyId: properties[10]?.id,
      date: addDays(today, -1),
      time: "2:00 PM",
      duration: 60,
      location: properties[10]?.address_street_complete || "321 Pine St",
      notes: "Client postponed due to travel",
      status: "cancelled"
    }
  ];
};

export default function Schedule() {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [appointments] = useState<Appointment[]>(generateMockAppointments());
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  
  const clients = getMockClients();
  const properties = getMockProperties();
  
  // Filter appointments
  const filteredAppointments = appointments.filter(apt => {
    const matchesType = filterType === "all" || apt.type === filterType;
    const matchesStatus = filterStatus === "all" || apt.status === filterStatus;
    return matchesType && matchesStatus;
  });
  
  // Get appointments for selected date
  const selectedDateAppointments = date 
    ? filteredAppointments.filter(apt => isSameDay(apt.date, date))
    : [];
    
  // Count appointments by status
  const statusCounts = {
    scheduled: appointments.filter(a => a.status === "scheduled").length,
    completed: appointments.filter(a => a.status === "completed").length,
    cancelled: appointments.filter(a => a.status === "cancelled").length,
  };
  
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "showing": return <Home className="h-4 w-4" />;
      case "meeting": return <User className="h-4 w-4" />;
      case "inspection": return <AlertCircle className="h-4 w-4" />;
      case "closing": return <CheckCircle className="h-4 w-4" />;
      default: return <CalendarIcon className="h-4 w-4" />;
    }
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "scheduled":
        return <Badge variant="default">Scheduled</Badge>;
      case "completed":
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Completed</Badge>;
      case "cancelled":
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };
  
  const handleAddAppointment = () => {
    toast.success("Appointment added successfully!");
    setShowAddDialog(false);
  };

  return (
    <LuxuryLayout>
      <div className="space-y-8">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Schedule</h2>
            <p className="text-muted-foreground">
              Manage your appointments and showings
            </p>
          </div>
          
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700">
                <Plus className="mr-2 h-4 w-4" />
                New Appointment
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>Add New Appointment</DialogTitle>
                <DialogDescription>
                  Schedule a new appointment, showing, or meeting.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="title" className="text-right">
                    Title
                  </Label>
                  <Input id="title" className="col-span-3" placeholder="Property showing..." />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="type" className="text-right">
                    Type
                  </Label>
                  <Select>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="showing">Property Showing</SelectItem>
                      <SelectItem value="meeting">Client Meeting</SelectItem>
                      <SelectItem value="inspection">Inspection</SelectItem>
                      <SelectItem value="closing">Closing</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="client" className="text-right">
                    Client
                  </Label>
                  <Select>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select client" />
                    </SelectTrigger>
                    <SelectContent>
                      {clients.slice(0, 10).map(client => (
                        <SelectItem key={client.id} value={client.id.toString()}>
                          {client.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="notes" className="text-right">
                    Notes
                  </Label>
                  <Textarea id="notes" className="col-span-3" placeholder="Additional details..." />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddAppointment}>Add Appointment</Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
        
        {/* Status Overview */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statusCounts.scheduled}</div>
              <p className="text-xs text-muted-foreground">Upcoming appointments</p>
            </CardContent>
          </Card>
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statusCounts.completed}</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cancelled</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statusCounts.cancelled}</div>
              <p className="text-xs text-muted-foreground">Need rescheduling</p>
            </CardContent>
          </Card>
        </div>
        
        {/* Main Content */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Calendar */}
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Calendar</CardTitle>
              <CardDescription>Select a date to view appointments</CardDescription>
            </CardHeader>
            <CardContent>
              <Calendar
                mode="single"
                selected={date}
                onSelect={setDate}
                className="rounded-md border"
              />
            </CardContent>
          </Card>
          
          {/* Appointments List */}
          <div className="lg:col-span-2 space-y-4">
            {/* Filters */}
            <div className="flex gap-4">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="showing">Showings</SelectItem>
                  <SelectItem value="meeting">Meetings</SelectItem>
                  <SelectItem value="inspection">Inspections</SelectItem>
                  <SelectItem value="closing">Closings</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Selected Date Appointments */}
            {date && (
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{format(date, "EEEE, MMMM d, yyyy")}</span>
                    <Badge variant="outline">{selectedDateAppointments.length} appointments</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedDateAppointments.length === 0 ? (
                    <p className="text-muted-foreground text-center py-8">
                      No appointments scheduled for this date
                    </p>
                  ) : (
                    <div className="space-y-4">
                      {selectedDateAppointments.map(appointment => {
                        const client = clients.find(c => c.id === appointment.clientId);
                        const property = properties.find(p => p.id === appointment.propertyId);
                        
                        return (
                          <div key={appointment.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div className="flex items-start justify-between">
                              <div className="flex items-start gap-3">
                                <div className="p-2 bg-cyan-100 rounded-full">
                                  {getTypeIcon(appointment.type)}
                                </div>
                                <div>
                                  <h4 className="font-semibold">{appointment.title}</h4>
                                  <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                                    <span className="flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      {appointment.time} ({appointment.duration} min)
                                    </span>
                                    <span className="flex items-center gap-1">
                                      <MapPin className="h-3 w-3" />
                                      {appointment.location}
                                    </span>
                                  </div>
                                  {client && (
                                    <p className="text-sm mt-2">
                                      Client: <span className="font-medium">{client.name}</span>
                                    </p>
                                  )}
                                  {appointment.notes && (
                                    <p className="text-sm text-muted-foreground mt-2">{appointment.notes}</p>
                                  )}
                                </div>
                              </div>
                              {getStatusBadge(appointment.status)}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
            
            {/* All Appointments */}
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>All Appointments</CardTitle>
                <CardDescription>Your complete appointment history</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredAppointments.map(appointment => {
                    const client = clients.find(c => c.id === appointment.clientId);
                    
                    return (
                      <div key={appointment.id} className="border-b pb-4 last:border-0 last:pb-0">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium">{appointment.title}</h4>
                            <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                              <span>{format(appointment.date, "MMM d, yyyy")}</span>
                              <span>{appointment.time}</span>
                              {client && <span>• {client.name}</span>}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {getTypeIcon(appointment.type)}
                            {getStatusBadge(appointment.status)}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </LuxuryLayout>
  );
}