import { useState, useEffect } from "react";
import { LuxuryLayout } from "@/components/LuxuryLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { HealthCheck } from "@/components/HealthCheck";
import { getMockClients, getMockProperties } from "@/lib/mockData";
import { 
  BarChart, Bar, LineChart, Line, PieChart, Pie, Cell, 
  XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer 
} from "recharts";
import { 
  Users, Home, DollarSign, Calendar, TrendingUp, 
  MapPin, Star, Clock, CheckCircle, AlertCircle 
} from "lucide-react";

export default function Dashboard() {
  const [clients] = useState(getMockClients());
  const [properties] = useState(getMockProperties());

  // Calculate real metrics
  const activeClients = clients.filter(c => c.status === "active").length;
  const totalPropertyValue = properties.reduce((sum, p) => sum + parseFloat(p.list_price), 0);
  const avgPropertyPrice = totalPropertyValue / properties.length;
  const activeListings = properties.filter(p => p.status === "Active").length;
  const pendingListings = properties.filter(p => p.status === "Pending").length;
  const soldListings = properties.filter(p => p.status === "Sold").length;
  
  // Calculate client budget distribution
  const budgetRanges = {
    "Under $500K": 0,
    "$500K - $1M": 0,
    "$1M - $2M": 0,
    "$2M - $5M": 0,
    "Over $5M": 0
  };

  clients.forEach(client => {
    const budget = client.budget;
    if (budget < 500000) budgetRanges["Under $500K"]++;
    else if (budget < 1000000) budgetRanges["$500K - $1M"]++;
    else if (budget < 2000000) budgetRanges["$1M - $2M"]++;
    else if (budget < 5000000) budgetRanges["$2M - $5M"]++;
    else budgetRanges["Over $5M"]++;
  });

  const budgetData = Object.entries(budgetRanges).map(([range, count]) => ({
    range,
    count,
    percentage: Math.round((count / clients.length) * 100)
  }));

  // Calculate properties by price range
  const priceRanges = {
    "Under $500K": 0,
    "$500K - $1M": 0,
    "$1M - $2M": 0,
    "$2M - $5M": 0,
    "Over $5M": 0
  };

  properties.forEach(property => {
    const price = parseFloat(property.list_price);
    if (price < 500000) priceRanges["Under $500K"]++;
    else if (price < 1000000) priceRanges["$500K - $1M"]++;
    else if (price < 2000000) priceRanges["$1M - $2M"]++;
    else if (price < 5000000) priceRanges["$2M - $5M"]++;
    else priceRanges["Over $5M"]++;
  });

  const priceData = Object.entries(priceRanges).map(([range, count]) => ({
    range,
    count
  }));

  // Calculate listings by city
  const cityListings = properties.reduce((acc, property) => {
    const city = property.address_city;
    acc[city] = (acc[city] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const cityData = Object.entries(cityListings)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 6)
    .map(([city, count]) => ({
      city,
      count
    }));

  // Colors for charts
  const COLORS = ['#0891b2', '#06b6d4', '#22d3ee', '#67e8f9', '#a5f3fc'];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(value);
  };

  return (
    <LuxuryLayout>
      <div className="space-y-8 animate-fade-in">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          <p className="text-muted-foreground">
            Real-time insights into your real estate business
          </p>
        </div>

        {/* System Health Check */}
        <div className="flex justify-center">
          <HealthCheck />
        </div>

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Clients</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeClients}</div>
              <p className="text-xs text-muted-foreground">
                {clients.length} total clients
              </p>
            </CardContent>
          </Card>
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Listings</CardTitle>
              <Home className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeListings}</div>
              <p className="text-xs text-muted-foreground">
                {pendingListings} pending, {soldListings} sold
              </p>
            </CardContent>
          </Card>
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Portfolio Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalPropertyValue)}</div>
              <p className="text-xs text-muted-foreground">
                Across all active listings
              </p>
            </CardContent>
          </Card>
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Property Price</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(avgPropertyPrice)}</div>
              <p className="text-xs text-muted-foreground">
                Current market average
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid gap-4 md:grid-cols-2">
          {/* Properties by Price Range */}
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Properties by Price Range</CardTitle>
              <CardDescription>Distribution of current listings</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={priceData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="range" tick={{ fontSize: 12 }} />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip 
                    formatter={(value) => [`${value} properties`, 'Count']}
                    contentStyle={{ backgroundColor: 'rgba(255,255,255,0.95)', borderRadius: '8px' }}
                  />
                  <Bar dataKey="count" fill="#0891b2" radius={[8, 8, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Client Budget Distribution */}
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Client Budget Distribution</CardTitle>
              <CardDescription>Understanding your client base</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={budgetData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="count"
                  >
                    {budgetData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value, name, props) => [
                      `${value} clients (${props.payload.percentage}%)`, 
                      props.payload.range
                    ]}
                    contentStyle={{ backgroundColor: 'rgba(255,255,255,0.95)', borderRadius: '8px' }}
                  />
                </PieChart>
              </ResponsiveContainer>
              <div className="mt-4 grid grid-cols-2 gap-2">
                {budgetData.map((entry, index) => (
                  <div key={entry.range} className="flex items-center gap-2 text-sm">
                    <div 
                      className="w-3 h-3 rounded" 
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    />
                    <span className="text-muted-foreground">{entry.range}: {entry.count}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Listings by City */}
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Listings by City</CardTitle>
            <CardDescription>Geographic distribution of your properties</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={cityData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis type="number" tick={{ fontSize: 12 }} />
                <YAxis dataKey="city" type="category" tick={{ fontSize: 12 }} width={120} />
                <Tooltip 
                  formatter={(value) => [`${value} properties`, 'Count']}
                  contentStyle={{ backgroundColor: 'rgba(255,255,255,0.95)', borderRadius: '8px' }}
                />
                <Bar dataKey="count" fill="#06b6d4" radius={[0, 8, 8, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Recent Client Activity</CardTitle>
              <CardDescription>Latest updates from your clients</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {clients.slice(0, 5).map((client) => (
                  <div key={client.id} className="flex items-center gap-4">
                    <div className="p-2 bg-cyan-100 rounded-full">
                      <Users className="h-4 w-4 text-cyan-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{client.name}</p>
                      <p className="text-xs text-muted-foreground">
                        Budget: {formatCurrency(client.budget)} • {client.preferences.propertyType}
                      </p>
                    </div>
                    {client.status === "active" ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-yellow-500" />
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>New Listings This Week</CardTitle>
              <CardDescription>Recently added properties</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {properties
                  .filter(p => p.dom <= 7)
                  .slice(0, 5)
                  .map((property) => (
                    <div key={property.id} className="flex items-center gap-4">
                      <div className="p-2 bg-orange-100 rounded-full">
                        <Home className="h-4 w-4 text-orange-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium line-clamp-1">
                          {property.address_street_complete}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatCurrency(parseFloat(property.list_price))} • {property.dom} days
                        </p>
                      </div>
                      <Badge variant={property.status === "Active" ? "default" : "secondary"}>
                        {property.status}
                      </Badge>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </LuxuryLayout>
  );
}