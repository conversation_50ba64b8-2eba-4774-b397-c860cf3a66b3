
import { useState } from "react";
import { LuxuryLayout } from "@/components/LuxuryLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { getMockClients, getMockProperties } from "@/lib/mockData";
import { 
  BarChart, Bar, LineChart, Line, PieChart, Pie, Cell, AreaChart, Area,
  XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend 
} from "recharts";
import { 
  TrendingUp, Users, Home, DollarSign, Calendar, Target, 
  Download, FileText, Filter, PieChartIcon, BarChart2 
} from "lucide-react";
import { format, subMonths, startOfMonth, endOfMonth } from "date-fns";

export default function Reports() {
  const [timeRange, setTimeRange] = useState("last6months");
  const [reportType, setReportType] = useState("overview");
  
  const clients = getMockClients();
  const properties = getMockProperties();
  
  // Generate monthly data for the last 6 months
  const generateMonthlyData = () => {
    const data = [];
    for (let i = 5; i >= 0; i--) {
      const date = subMonths(new Date(), i);
      const monthName = format(date, "MMM");
      
      // Simulate data
      data.push({
        month: monthName,
        listings: Math.floor(Math.random() * 20) + 10,
        sales: Math.floor(Math.random() * 10) + 5,
        revenue: Math.floor(Math.random() * 500000) + 200000,
        newClients: Math.floor(Math.random() * 15) + 5,
      });
    }
    return data;
  };
  
  const monthlyData = generateMonthlyData();
  
  // Client analytics
  const clientsByType = {
    "Buyers": clients.filter(c => c.client_type === "buyer").length,
    "Sellers": clients.filter(c => c.client_type === "seller").length,
    "Investors": clients.filter(c => c.client_type === "investor").length,
    "Both": clients.filter(c => c.client_type === "both").length,
  };
  
  const clientsByStage = {
    "Lead": clients.filter(c => c.stage === "lead").length,
    "Qualified": clients.filter(c => c.stage === "qualified").length,
    "Active": clients.filter(c => c.stage === "active").length,
    "Closed": clients.filter(c => c.stage === "closed").length,
  };
  
  const clientTypeData = Object.entries(clientsByType).map(([type, count]) => ({
    name: type,
    value: count,
    percentage: Math.round((count / clients.length) * 100)
  }));
  
  const clientStageData = Object.entries(clientsByStage).map(([stage, count]) => ({
    name: stage,
    value: count,
  }));
  
  // Property analytics
  const propertyPriceRanges = [
    { range: "Under $500K", count: properties.filter(p => parseFloat(p.list_price) < 500000).length },
    { range: "$500K-$1M", count: properties.filter(p => parseFloat(p.list_price) >= 500000 && parseFloat(p.list_price) < 1000000).length },
    { range: "$1M-$2M", count: properties.filter(p => parseFloat(p.list_price) >= 1000000 && parseFloat(p.list_price) < 2000000).length },
    { range: "Over $2M", count: properties.filter(p => parseFloat(p.list_price) >= 2000000).length },
  ];
  
  const avgDaysOnMarket = Math.round(properties.reduce((sum, p) => sum + p.dom, 0) / properties.length);
  const totalListingValue = properties.reduce((sum, p) => sum + parseFloat(p.list_price), 0);
  const avgListingPrice = totalListingValue / properties.length;
  
  // Top performing cities
  const cityCounts = properties.reduce((acc, property) => {
    acc[property.address_city] = (acc[property.address_city] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const topCities = Object.entries(cityCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([city, count]) => ({ city, listings: count }));
  
  // Colors for charts
  const COLORS = ['#0891b2', '#06b6d4', '#22d3ee', '#67e8f9'];
  const CHART_COLORS = {
    primary: '#0891b2',
    secondary: '#06b6d4',
    tertiary: '#22d3ee',
    quaternary: '#67e8f9',
  };
  
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(value);
  };
  
  const handleExport = () => {
    // Simulate export functionality
    alert("Report exported successfully!");
  };

  return (
    <LuxuryLayout>
      <div className="space-y-8">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Reports & Analytics</h2>
            <p className="text-muted-foreground">
              Comprehensive insights into your real estate business
            </p>
          </div>
          
          <div className="flex gap-4">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="last30days">Last 30 Days</SelectItem>
                <SelectItem value="last3months">Last 3 Months</SelectItem>
                <SelectItem value="last6months">Last 6 Months</SelectItem>
                <SelectItem value="lastyear">Last Year</SelectItem>
              </SelectContent>
            </Select>
            
            <Button onClick={handleExport} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
          </div>
        </div>
        
        {/* Report Type Selector */}
        <div className="flex gap-4">
          <Button 
            variant={reportType === "overview" ? "default" : "outline"}
            onClick={() => setReportType("overview")}
          >
            <BarChart2 className="mr-2 h-4 w-4" />
            Overview
          </Button>
          <Button 
            variant={reportType === "clients" ? "default" : "outline"}
            onClick={() => setReportType("clients")}
          >
            <Users className="mr-2 h-4 w-4" />
            Client Analytics
          </Button>
          <Button 
            variant={reportType === "properties" ? "default" : "outline"}
            onClick={() => setReportType("properties")}
          >
            <Home className="mr-2 h-4 w-4" />
            Property Analytics
          </Button>
          <Button 
            variant={reportType === "performance" ? "default" : "outline"}
            onClick={() => setReportType("performance")}
          >
            <Target className="mr-2 h-4 w-4" />
            Performance
          </Button>
        </div>
        
        {/* Overview Report */}
        {reportType === "overview" && (
          <>
            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatCurrency(monthlyData.reduce((sum, m) => sum + m.revenue, 0))}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    +20.1% from last period
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {monthlyData.reduce((sum, m) => sum + m.sales, 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    +15% from last period
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">New Clients</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {monthlyData.reduce((sum, m) => sum + m.newClients, 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    +12.5% from last period
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Listings</CardTitle>
                  <Home className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {properties.filter(p => p.status === "Active").length}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {avgDaysOnMarket} avg days on market
                  </p>
                </CardContent>
              </Card>
            </div>
            
            {/* Revenue Trend */}
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
                <CardDescription>Monthly revenue over the selected period</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={monthlyData}>
                    <defs>
                      <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.8}/>
                        <stop offset="95%" stopColor={CHART_COLORS.primary} stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Area type="monotone" dataKey="revenue" stroke={CHART_COLORS.primary} fillOpacity={1} fill="url(#colorRevenue)" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            {/* Sales vs Listings */}
            <div className="grid gap-4 md:grid-cols-2">
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>Sales vs Listings</CardTitle>
                  <CardDescription>Monthly comparison</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={monthlyData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="listings" stroke={CHART_COLORS.primary} name="Listings" />
                      <Line type="monotone" dataKey="sales" stroke={CHART_COLORS.secondary} name="Sales" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
              
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>Top Cities by Listings</CardTitle>
                  <CardDescription>Most active markets</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={topCities}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="city" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="listings" fill={CHART_COLORS.primary} radius={[8, 8, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </>
        )}
        
        {/* Client Analytics */}
        {reportType === "clients" && (
          <>
            <div className="grid gap-4 md:grid-cols-2">
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>Client Distribution by Type</CardTitle>
                  <CardDescription>Breakdown of your client base</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={clientTypeData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name} ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {clientTypeData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
              
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>Client Pipeline</CardTitle>
                  <CardDescription>Clients by stage in the sales process</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={clientStageData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="value" fill={CHART_COLORS.secondary} radius={[8, 8, 0, 0]}>
                        {clientStageData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
            
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Client Acquisition Trend</CardTitle>
                <CardDescription>New clients added each month</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={monthlyData}>
                    <defs>
                      <linearGradient id="colorClients" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={CHART_COLORS.secondary} stopOpacity={0.8}/>
                        <stop offset="95%" stopColor={CHART_COLORS.secondary} stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="newClients" stroke={CHART_COLORS.secondary} fillOpacity={1} fill="url(#colorClients)" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </>
        )}
        
        {/* Property Analytics */}
        {reportType === "properties" && (
          <>
            <div className="grid gap-4 md:grid-cols-3">
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Portfolio Value</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(totalListingValue)}</div>
                  <p className="text-xs text-muted-foreground">
                    Across all listings
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average Price</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(avgListingPrice)}</div>
                  <p className="text-xs text-muted-foreground">
                    Per listing
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Days on Market</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{avgDaysOnMarket}</div>
                  <p className="text-xs text-muted-foreground">
                    days average
                  </p>
                </CardContent>
              </Card>
            </div>
            
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Properties by Price Range</CardTitle>
                <CardDescription>Distribution of listings by price bracket</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={propertyPriceRanges}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="range" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill={CHART_COLORS.tertiary} radius={[8, 8, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Market Activity by City</CardTitle>
                <CardDescription>Number of active listings per location</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={topCities} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis type="number" />
                    <YAxis dataKey="city" type="category" width={100} />
                    <Tooltip />
                    <Bar dataKey="listings" fill={CHART_COLORS.quaternary} radius={[0, 8, 8, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </>
        )}
        
        {/* Performance Report */}
        {reportType === "performance" && (
          <>
            <div className="grid gap-4 md:grid-cols-4">
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">68%</div>
                  <p className="text-xs text-muted-foreground">
                    Lead to client
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Close Rate</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">45%</div>
                  <p className="text-xs text-muted-foreground">
                    Listing to sale
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Sale Time</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">42 days</div>
                  <p className="text-xs text-muted-foreground">
                    From listing to close
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Client Satisfaction</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">4.8/5</div>
                  <p className="text-xs text-muted-foreground">
                    Average rating
                  </p>
                </CardContent>
              </Card>
            </div>
            
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Monthly Performance Metrics</CardTitle>
                <CardDescription>Key performance indicators over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={monthlyData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Line 
                      yAxisId="left"
                      type="monotone" 
                      dataKey="sales" 
                      stroke={CHART_COLORS.primary} 
                      name="Sales"
                      strokeWidth={2}
                    />
                    <Line 
                      yAxisId="left"
                      type="monotone" 
                      dataKey="listings" 
                      stroke={CHART_COLORS.secondary} 
                      name="New Listings"
                      strokeWidth={2}
                    />
                    <Line 
                      yAxisId="right"
                      type="monotone" 
                      dataKey="newClients" 
                      stroke={CHART_COLORS.tertiary} 
                      name="New Clients"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </LuxuryLayout>
  );
}
