'use client';

import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  MapPin, 
  Users, 
  Calendar,
  DollarSign,
  Home,
  Phone,
  Mail,
  Clock,
  Star,
  ArrowRight,
  Thermometer,
  Sun,
  Building2
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface DashboardStats {
  activeListings: number;
  qualifiedLeads: number;
  appointments: number;
  salesVolume: string;
}

interface HotLead {
  name: string;
  property: string;
  score: number;
  lastContact: string;
}

interface Appointment {
  time: string;
  client: string;
  type: string;
  location: string;
}

export const LuxuryDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    activeListings: 0,
    qualifiedLeads: 0,
    appointments: 0,
    salesVolume: '$0'
  });
  
  const [hotLeads, setHotLeads] = useState<HotLead[]>([]);
  const [todaySchedule, setTodaySchedule] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load dashboard data immediately
    const loadData = () => {
      // Mock data for now - replace with actual API calls
      setStats({
        activeListings: 24,
        qualifiedLeads: 18,
        appointments: 7,
        salesVolume: '$2.4M'
      });

      setHotLeads([
        { name: "Emily Chen", property: "Luxury Downtown Condo", score: 95, lastContact: "2 hours ago" },
        { name: "Marcus Rodriguez", property: "Suburban Family Home", score: 88, lastContact: "1 day ago" },
        { name: "Sarah Kim", property: "Modern Townhouse", score: 82, lastContact: "3 hours ago" }
      ]);

      setTodaySchedule([
        { time: "10:00 AM", client: "John & Lisa Martinez", type: "Property Showing", location: "123 Oak Street" },
        { time: "2:30 PM", client: "David Thompson", type: "Contract Signing", location: "Office" },
        { time: "4:00 PM", client: "Anna Wilson", type: "Market Analysis", location: "Virtual" }
      ]);

      setLoading(false);
    };

    // Add a small delay to ensure proper rendering
    setTimeout(loadData, 100);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-luxury-navy/60">Loading dashboard...</div>
      </div>
    );
  }

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Creative Hero Section */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-luxury-navy via-luxury-navy-light to-luxury-navy p-8 text-white">
        <div className="absolute inset-0 bg-gradient-to-r from-luxury-gold/10 to-transparent" />
        <div className="absolute top-4 right-4 opacity-20">
          <Building2 className="h-32 w-32" />
        </div>
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold mb-2">Good morning, Sarah!</h1>
              <p className="text-luxury-cream/80 text-lg">Ready to close some deals today?</p>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2 text-luxury-gold mb-2">
                <Sun className="h-5 w-5" />
                <span className="text-lg font-medium">72°F</span>
              </div>
              <p className="text-luxury-cream/60">Perfect showing weather</p>
            </div>
          </div>
        </div>
      </div>

      {/* Creative Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="relative overflow-hidden group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100/50">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-green-600/10 group-hover:from-green-500/10 group-hover:to-green-600/20 transition-all duration-300" />
          <CardContent className="p-6 relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-500 rounded-2xl">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-800">+12%</Badge>
            </div>
            <h3 className="text-3xl font-bold text-luxury-navy mb-1">{stats.activeListings}</h3>
            <p className="text-luxury-navy/60">Active Listings</p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100/50">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 group-hover:from-blue-500/10 group-hover:to-blue-600/20 transition-all duration-300" />
          <CardContent className="p-6 relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-500 rounded-2xl">
                <Users className="h-6 w-6 text-white" />
              </div>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">Hot</Badge>
            </div>
            <h3 className="text-3xl font-bold text-luxury-navy mb-1">{stats.qualifiedLeads}</h3>
            <p className="text-luxury-navy/60">Qualified Leads</p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100/50">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10 group-hover:from-purple-500/10 group-hover:to-purple-600/20 transition-all duration-300" />
          <CardContent className="p-6 relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-500 rounded-2xl">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">Today</Badge>
            </div>
            <h3 className="text-3xl font-bold text-luxury-navy mb-1">{stats.appointments}</h3>
            <p className="text-luxury-navy/60">Appointments</p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-luxury-gold/10 to-luxury-gold/20">
          <div className="absolute inset-0 bg-gradient-to-br from-luxury-gold/5 to-luxury-gold/15 group-hover:from-luxury-gold/15 group-hover:to-luxury-gold/25 transition-all duration-300" />
          <CardContent className="p-6 relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-luxury-gold rounded-2xl">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <Badge variant="secondary" className="bg-luxury-gold/20 text-luxury-navy">MTD</Badge>
            </div>
            <h3 className="text-3xl font-bold text-luxury-navy mb-1">{stats.salesVolume}</h3>
            <p className="text-luxury-navy/60">Sales Volume</p>
          </CardContent>
        </Card>
      </div>

      {/* Creative Two-Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Hot Leads Section - Takes 2 columns */}
        <div className="lg:col-span-2">
          <Card className="border-0 glass-card overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-red-500/5 to-orange-500/5 border-b border-red-100">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-luxury-navy">
                  <div className="p-2 bg-red-500 rounded-xl">
                    <Thermometer className="h-4 w-4 text-white" />
                  </div>
                  Hottest Leads
                </CardTitle>
                <Button variant="ghost" size="sm" className="text-luxury-gold hover:text-luxury-gold-light">
                  View All <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {hotLeads.map((lead, index) => (
                <div key={index} className="p-6 border-b border-gray-100 last:border-0 hover:bg-gradient-to-r hover:from-red-50/50 hover:to-transparent transition-all duration-200 group">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="relative">
                        <div className="h-12 w-12 bg-gradient-to-br from-luxury-navy to-luxury-navy-light rounded-full flex items-center justify-center text-white font-bold">
                          {lead.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full flex items-center justify-center">
                          <span className="text-xs text-white font-bold">{lead.score}</span>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-luxury-navy group-hover:text-luxury-gold transition-colors">{lead.name}</h4>
                        <p className="text-luxury-navy/60 text-sm">{lead.property}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-1 text-luxury-gold mb-1">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className={`h-3 w-3 ${i < Math.floor(lead.score / 20) ? 'fill-current' : 'opacity-20'}`} />
                        ))}
                      </div>
                      <p className="text-xs text-luxury-navy/60">{lead.lastContact}</p>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Today's Schedule - Takes 1 column */}
        <div className="lg:col-span-1">
          <Card className="border-0 glass-card h-full">
            <CardHeader className="bg-gradient-to-r from-blue-500/5 to-purple-500/5 border-b border-blue-100">
              <CardTitle className="flex items-center gap-2 text-luxury-navy">
                <div className="p-2 bg-blue-500 rounded-xl">
                  <Clock className="h-4 w-4 text-white" />
                </div>
                Today's Schedule
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {todaySchedule.map((appointment, index) => (
                <div key={index} className="p-4 border-b border-gray-100 last:border-0 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-transparent transition-all duration-200 group">
                  <div className="flex items-start gap-3">
                    <div className="text-center min-w-[60px]">
                      <div className="text-sm font-bold text-luxury-navy">{appointment.time}</div>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-luxury-navy group-hover:text-blue-600 transition-colors">{appointment.client}</h4>
                      <p className="text-xs text-luxury-navy/60 mb-1">{appointment.type}</p>
                      <div className="flex items-center gap-1 text-xs text-luxury-navy/50">
                        <MapPin className="h-3 w-3" />
                        {appointment.location}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Creative Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Button className="h-20 bg-gradient-to-br from-luxury-navy to-luxury-navy-light hover:from-luxury-navy-light hover:to-luxury-navy text-white border-0 rounded-2xl group">
          <div className="flex flex-col items-center gap-2">
            <Home className="h-6 w-6 group-hover:scale-110 transition-transform" />
            <span className="text-sm font-medium">Add Property</span>
          </div>
        </Button>
        
        <Button className="h-20 bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white border-0 rounded-2xl group">
          <div className="flex flex-col items-center gap-2">
            <Phone className="h-6 w-6 group-hover:scale-110 transition-transform" />
            <span className="text-sm font-medium">Call Client</span>
          </div>
        </Button>
        
        <Button className="h-20 bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white border-0 rounded-2xl group">
          <div className="flex flex-col items-center gap-2">
            <Mail className="h-6 w-6 group-hover:scale-110 transition-transform" />
            <span className="text-sm font-medium">Send Email</span>
          </div>
        </Button>
        
        <Button className="h-20 bg-gradient-to-br from-luxury-gold to-luxury-gold-light hover:from-luxury-gold-light hover:to-luxury-gold text-white border-0 rounded-2xl group">
          <div className="flex flex-col items-center gap-2">
            <Calendar className="h-6 w-6 group-hover:scale-110 transition-transform" />
            <span className="text-sm font-medium">Schedule</span>
          </div>
        </Button>
      </div>
    </div>
  );
};