'use client';

import React from 'react';

export const SimpleLuxuryDashboard: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* Simple Hero Section */}
      <div className="bg-luxury-navy p-8 rounded-lg text-white">
        <h1 className="text-3xl font-bold mb-2">Real Estate Dashboard</h1>
        <p className="text-luxury-cream/80">Welcome to your luxury CRM</p>
      </div>

      {/* Simple Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-luxury-navy">24</div>
          <div className="text-luxury-navy/60">Active Listings</div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-luxury-navy">18</div>
          <div className="text-luxury-navy/60">Qualified Leads</div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-luxury-navy">7</div>
          <div className="text-luxury-navy/60">Appointments</div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-luxury-navy">$2.4M</div>
          <div className="text-luxury-navy/60">Sales Volume</div>
        </div>
      </div>

      {/* Simple Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-luxury-navy mb-4">Hot Leads</h2>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="font-medium">Emily Chen</span>
              <span className="text-luxury-gold">95% match</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Marcus Rodriguez</span>
              <span className="text-luxury-gold">88% match</span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-luxury-navy mb-4">Today's Schedule</h2>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="font-medium">10:00 AM</span>
              <span className="text-luxury-navy/60">Property Showing</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">2:30 PM</span>
              <span className="text-luxury-navy/60">Contract Signing</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};