
import React from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
} from "@/components/ui/table";

async function fetchAppointments() {
  const { data, error } = await supabase.from("appointments").select("id,scheduled_date,appointment_type,location,status").limit(25).order("scheduled_date", { ascending: false });
  if (error) throw error;
  return data;
}

export function ScheduleList() {
  const { data, isLoading, error } = useQuery({
    queryKey: ["appointments"],
    queryFn: fetchAppointments,
  });

  if (isLoading) return <div className="text-center py-8">Loading appointments...</div>;
  if (error) return <div className="text-center py-8 text-red-500">Error loading appointments.</div>;
  if (!data || data.length === 0) return <div className="text-center py-8 text-muted-foreground">No appointments found.</div>;

  return (
    <div className="bg-white/70 rounded-xl border border-blue-100/70 shadow-sm p-4 mt-4 animate-fade-in">
      <Table>
        <TableCaption>Upcoming Appointments</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((a: any) => (
            <TableRow key={a.id}>
              <TableCell>{a.scheduled_date ? new Date(a.scheduled_date).toLocaleString() : "—"}</TableCell>
              <TableCell>{a.appointment_type ?? "—"}</TableCell>
              <TableCell>{a.location ?? "—"}</TableCell>
              <TableCell>{a.status ?? "—"}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
