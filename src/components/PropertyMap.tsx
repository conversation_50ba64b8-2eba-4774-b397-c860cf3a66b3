import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default icon issue with webpack
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
});

interface PropertyMapProps {
  address_street_complete: string;
  address_city: string;
  address_zip_code: string;
  latitude?: number;
  longitude?: number;
}

const DEFAULT_CENTER: L.LatLngTuple = [37.7749, -122.4194]; // Default to San Francisco
const DEFAULT_ZOOM = 13;

const ChangeView: React.FC<{ center: L.LatLngTuple; zoom: number }> = ({ center, zoom }) => {
  const map = useMap();
  map.setView(center, zoom);
  return null;
};

const PropertyMap: React.FC<PropertyMapProps> = ({
  address_street_complete,
  address_city,
  address_zip_code,
  latitude,
  longitude,
}) => {
  const [position, setPosition] = useState<L.LatLngTuple | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fullAddress = `${address_street_complete}, ${address_city}, CA ${address_zip_code}`;

  useEffect(() => {
    if (latitude && longitude) {
      setPosition([latitude, longitude]);
      setIsLoading(false);
    } else {
      const geocodeAddress = async () => {
        setIsLoading(true);
        setError(null);
        const encodedAddress = encodeURIComponent(fullAddress);
        const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodedAddress}&countrycodes=us&limit=1`;

        try {
          const response = await fetch(url, {
            headers: {
              'User-Agent': 'YourAppName/1.0 (<EMAIL>)' // Replace with your app name and email
            }
          });
          if (!response.ok) {
            throw new Error(`Geocoding failed: ${response.statusText}`);
          }
          const data = await response.json();
          if (data && data.length > 0) {
            const { lat, lon } = data[0];
            setPosition([parseFloat(lat), parseFloat(lon)]);
          } else {
            throw new Error('No results found for the address.');
          }
        } catch (err) {
          console.error('Geocoding error:', err);
          setError(`Could not find location on map for "${fullAddress}".`);
        } finally {
          setIsLoading(false);
        }
      };
      geocodeAddress();
    }
  }, [latitude, longitude, fullAddress]);

  if (isLoading) {
    return (
      <div className="h-64 bg-white/80 backdrop-blur-sm rounded-lg flex items-center justify-center">
        <p>Loading map...</p>
      </div>
    );
  }

  return (
    <div className="h-64 bg-white/80 backdrop-blur-sm rounded-lg">
      {error && !position && (
        <div className="h-full flex items-center justify-center p-4">
          <p className="text-red-500 text-center">{error}</p>
        </div>
      )}
      {!error && position && (
        <MapContainer
          center={position || DEFAULT_CENTER}
          zoom={DEFAULT_ZOOM}
          scrollWheelZoom={true}
          style={{ height: '100%', width: '100%', borderRadius: '0.5rem' }}
          className="rounded-lg" // Ensure rounded corners are applied to the map itself
        >
          <ChangeView center={position || DEFAULT_CENTER} zoom={DEFAULT_ZOOM} />
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          {position && (
            <Marker position={position}>
              <Popup>{fullAddress}</Popup>
            </Marker>
          )}
        </MapContainer>
      )}
       {!isLoading && !error && !position && (
         <div className="h-full flex items-center justify-center p-4">
          <p className="text-center">Map data unavailable or address not found.</p>
        </div>
       )}
    </div>
  );
};

export default PropertyMap;