import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getMockClients, getMockProperties } from "@/lib/mockData";
import { Search, X, Users, Home, Calendar, FileText } from "lucide-react";
import { cn } from "@/lib/utils";

interface SearchResult {
  id: string;
  type: "client" | "property" | "appointment" | "document";
  title: string;
  subtitle?: string;
  description?: string;
  route: string;
  icon: React.ReactNode;
}

export function GlobalSearch() {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();
  
  const [clients] = useState(() => getMockClients());
  const [properties] = useState(() => getMockProperties());
  
  // Close search when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);
  
  // Focus input when search opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);
  
  // Keyboard shortcut (Cmd/Ctrl + K)
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if ((event.metaKey || event.ctrlKey) && event.key === "k") {
        event.preventDefault();
        setIsOpen(true);
      }
      if (event.key === "Escape") {
        setIsOpen(false);
      }
    }
    
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);
  
  // Perform search
  useEffect(() => {
    if (!searchQuery) {
      setResults([]);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    const searchResults: SearchResult[] = [];
    
    // Search clients
    const matchingClients = clients.filter(client => 
      client.name?.toLowerCase().includes(query) ||
      client.email?.toLowerCase().includes(query) ||
      client.mobile_phone?.toLowerCase().includes(query) ||
      client.city?.toLowerCase().includes(query)
    ).slice(0, 3);
    
    matchingClients.forEach(client => {
      searchResults.push({
        id: client.id.toString(),
        type: "client",
        title: client.name || `${client.first_name} ${client.last_name}`,
        subtitle: client.email || client.mobile_phone || "",
        description: `${client.client_type} • ${client.status}`,
        route: `/clients/${client.id}`,
        icon: <Users className="h-4 w-4" />
      });
    });
    
    // Search properties
    const matchingProperties = properties.filter(property =>
      property.address_street_complete.toLowerCase().includes(query) ||
      property.address_city.toLowerCase().includes(query) ||
      property.subdivision?.toLowerCase().includes(query) ||
      property.architectural_style?.toLowerCase().includes(query)
    ).slice(0, 3);
    
    matchingProperties.forEach(property => {
      searchResults.push({
        id: property.id,
        type: "property",
        title: property.address_street_complete,
        subtitle: `${property.address_city}, CA ${property.address_zip_code}`,
        description: `$${parseInt(property.list_price).toLocaleString()} • ${property.bedrooms_and_possible_bedrooms} beds • ${property.full_bathrooms} baths`,
        route: `/properties/${property.id}`,
        icon: <Home className="h-4 w-4" />
      });
    });
    
    // Add quick navigation items
    const navigationItems = [
      { title: "Dashboard", route: "/dashboard", icon: <FileText className="h-4 w-4" /> },
      { title: "Schedule", route: "/schedule", icon: <Calendar className="h-4 w-4" /> },
      { title: "Properties", route: "/properties", icon: <Home className="h-4 w-4" /> },
      { title: "Clients", route: "/clients", icon: <Users className="h-4 w-4" /> },
    ];
    
    navigationItems.forEach(item => {
      if (item.title.toLowerCase().includes(query)) {
        searchResults.push({
          id: item.route,
          type: "document",
          title: item.title,
          subtitle: "Navigate to page",
          route: item.route,
          icon: item.icon
        });
      }
    });
    
    setResults(searchResults);
  }, [searchQuery, clients, properties]);
  
  const handleSelect = (result: SearchResult) => {
    navigate(result.route);
    setIsOpen(false);
    setSearchQuery("");
  };
  
  const getTypeColor = (type: string) => {
    switch (type) {
      case "client": return "bg-blue-100 text-blue-800";
      case "property": return "bg-green-100 text-green-800";
      case "appointment": return "bg-purple-100 text-purple-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };
  
  return (
    <div ref={searchRef} className="relative">
      <Button
        variant="outline"
        className="relative w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64"
        onClick={() => setIsOpen(true)}
      >
        <Search className="mr-2 h-4 w-4" />
        <span>Search...</span>
        <kbd className="pointer-events-none absolute right-1.5 top-1.5 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>
      
      {isOpen && (
        <div className="absolute top-12 left-0 right-0 z-50 min-w-[300px] lg:min-w-[500px]">
          <Card className="shadow-lg bg-white/95 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  ref={inputRef}
                  placeholder="Search clients, properties, or navigate..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1 border-0 focus:ring-0 p-0 h-auto text-base"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsOpen(false);
                    setSearchQuery("");
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            
            {results.length > 0 && (
              <CardContent className="pt-0">
                <div className="space-y-1">
                  {results.map((result) => (
                    <button
                      key={`${result.type}-${result.id}`}
                      className="w-full text-left rounded-lg p-3 hover:bg-gray-100 transition-colors flex items-start gap-3"
                      onClick={() => handleSelect(result)}
                    >
                      <div className="p-2 bg-gray-100 rounded">
                        {result.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <p className="font-medium truncate">{result.title}</p>
                          <Badge variant="secondary" className={cn("text-xs", getTypeColor(result.type))}>
                            {result.type}
                          </Badge>
                        </div>
                        {result.subtitle && (
                          <p className="text-sm text-muted-foreground truncate">{result.subtitle}</p>
                        )}
                        {result.description && (
                          <p className="text-xs text-muted-foreground truncate">{result.description}</p>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              </CardContent>
            )}
            
            {searchQuery && results.length === 0 && (
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground text-center py-6">
                  No results found for "{searchQuery}"
                </p>
              </CardContent>
            )}
          </Card>
        </div>
      )}
    </div>
  );
}