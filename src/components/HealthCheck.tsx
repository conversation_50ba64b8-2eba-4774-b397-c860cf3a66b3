import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { aiService } from '@/services/aiService';
import { realClientsData } from '@/data/realClientsData';
import { realPropertiesData } from '@/data/realPropertiesData';
import { parseNoteAddIntent } from '@/utils/noteHandler';
import { extractClientData } from '@/utils/clientCreator';

interface HealthStatus {
  component: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  message: string;
  details?: any;
}

export const HealthCheck: React.FC = () => {
  const [healthStatuses, setHealthStatuses] = useState<HealthStatus[]>([]);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  const runHealthCheck = async () => {
    const statuses: HealthStatus[] = [];

    // Check 1: Environment
    statuses.push({
      component: 'Container Environment',
      status: window.location.port === '5002' ? 'healthy' : 'degraded',
      message: `Running on port ${window.location.port}`,
      details: {
        expectedPort: '5002',
        actualPort: window.location.port,
        url: window.location.href
      }
    });

    // Check 2: Data Layer
    try {
      const clientCount = realClientsData.length;
      const propertyCount = realPropertiesData.length;
      
      statuses.push({
        component: 'Mock Data Layer',
        status: clientCount > 0 && propertyCount > 0 ? 'healthy' : 'unhealthy',
        message: `${clientCount} clients, ${propertyCount} properties`,
        details: { clientCount, propertyCount }
      });
    } catch (error) {
      statuses.push({
        component: 'Mock Data Layer',
        status: 'unhealthy',
        message: `Data loading failed: ${error}`,
      });
    }

    // Check 3: AI Service
    try {
      const response = await aiService.sendMessage('health check');
      statuses.push({
        component: 'AI Service',
        status: response.message ? 'healthy' : 'degraded',
        message: response.message ? 'AI responding' : 'AI not responding',
        details: {
          responseLength: response.message?.length || 0,
          intent: response.intent
        }
      });
    } catch (error) {
      statuses.push({
        component: 'AI Service',
        status: 'unhealthy',
        message: `AI service error: ${error}`,
      });
    }

    // Check 4: Enhanced Features
    try {
      const noteIntent = parseNoteAddIntent('Add note to test: health check');
      const clientData = extractClientData('Create client: Test User, <EMAIL>');
      
      statuses.push({
        component: 'Enhanced AI Features',
        status: noteIntent && clientData ? 'healthy' : 'degraded',
        message: noteIntent && clientData ? 'All features working' : 'Some features degraded',
        details: {
          noteIntentWorking: !!noteIntent,
          clientExtractionWorking: !!clientData
        }
      });
    } catch (error) {
      statuses.push({
        component: 'Enhanced AI Features',
        status: 'unhealthy',
        message: `Feature error: ${error}`,
      });
    }

    // Check 5: LocalStorage
    try {
      const testKey = 'health_check_test';
      const testValue = Date.now().toString();
      localStorage.setItem(testKey, testValue);
      const retrieved = localStorage.getItem(testKey);
      localStorage.removeItem(testKey);
      
      statuses.push({
        component: 'LocalStorage',
        status: retrieved === testValue ? 'healthy' : 'unhealthy',
        message: retrieved === testValue ? 'Storage working' : 'Storage failed',
      });
    } catch (error) {
      statuses.push({
        component: 'LocalStorage',
        status: 'unhealthy',
        message: `Storage error: ${error}`,
      });
    }

    setHealthStatuses(statuses);
    setLastCheck(new Date());
  };

  useEffect(() => {
    runHealthCheck();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(runHealthCheck, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: HealthStatus['status']) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'degraded': return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'unhealthy': return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusBadge = (status: HealthStatus['status']) => {
    switch (status) {
      case 'healthy': return <Badge className="bg-green-100 text-green-800">Healthy</Badge>;
      case 'degraded': return <Badge className="bg-yellow-100 text-yellow-800">Degraded</Badge>;
      case 'unhealthy': return <Badge className="bg-red-100 text-red-800">Unhealthy</Badge>;
    }
  };

  const overallStatus = healthStatuses.every(s => s.status === 'healthy') ? 'healthy' :
                       healthStatuses.some(s => s.status === 'unhealthy') ? 'unhealthy' : 'degraded';

  // Expose health data to window for external testing
  (window as any).healthCheck = {
    statuses: healthStatuses,
    lastCheck,
    overallStatus,
    refresh: runHealthCheck
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            🏥 System Health Check
            {getStatusBadge(overallStatus)}
          </CardTitle>
          {lastCheck && (
            <span className="text-sm text-muted-foreground">
              Last check: {lastCheck.toLocaleTimeString()}
            </span>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {healthStatuses.map((status, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded">
              <div className="flex items-center gap-3">
                {getStatusIcon(status.status)}
                <div>
                  <div className="font-medium">{status.component}</div>
                  <div className="text-sm text-muted-foreground">{status.message}</div>
                </div>
              </div>
              {getStatusBadge(status.status)}
            </div>
          ))}
        </div>
        
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
          <div className="text-sm">
            <strong>Container Validation:</strong> Access this health check at{' '}
            <code className="bg-blue-100 px-1 rounded">localhost:5002</code> or run{' '}
            <code className="bg-blue-100 px-1 rounded">window.healthCheck.refresh()</code> in browser console.
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
