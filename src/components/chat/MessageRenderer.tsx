import React from 'react';
import { Link } from 'react-router-dom';
import { parseEntityLinks } from '@/utils/entityRouteMapper';
import { ExternalLink } from 'lucide-react';

interface MessageRendererProps {
  content: string;
  className?: string;
}

export const MessageRenderer: React.FC<MessageRendererProps> = ({ content, className = '', ...props }) => {
  // Extract only the props we need, ignore any data-lov-id or other unwanted props
  const { parsedMessage, links } = parseEntityLinks(content);
  
  // Split message by link placeholders
  const parts = parsedMessage.split(/\{\{link:(\d+)\}\}/);
  
  return (
    <div className={className}>
      {parts.map((part, index) => {
        // Even indices are text, odd indices are link numbers
        if (index % 2 === 0) {
          // Regular text - split by newlines to handle formatting
          return (
            <React.Fragment key={index}>
              {part.split('\n').map((line, lineIndex) => (
                <React.Fragment key={lineIndex}>
                  {lineIndex > 0 && <br />}
                  {line.split(/\*\*([^*]+)\*\*/).map((segment, segmentIndex) => {
                    // Odd segments are bold text
                    if (segmentIndex % 2 === 1) {
                      return <strong key={segmentIndex}>{segment}</strong>;
                    }
                    return segment;
                  })}
                </React.Fragment>
              ))}
            </React.Fragment>
          );
        } else {
          // Link placeholder - get the link
          const linkIndex = parseInt(part);
          const link = links[linkIndex];
          
          if (!link) return null;
          
          return (
            <Link
              key={index}
              to={link.route}
              className="inline-flex items-center gap-1 px-2 py-0.5 bg-cyan-100 hover:bg-cyan-200 text-cyan-700 hover:text-cyan-800 rounded-md transition-colors duration-200 font-medium text-sm"
            >
              {link.text}
              <ExternalLink className="h-3 w-3" />
            </Link>
          );
        }
      })}
    </div>
  );
};