
import React from "react";
import { Bo<PERSON> } from "lucide-react";
import { useNavigate } from "react-router-dom";

export const AIAssistantButton: React.FC = () => {
  const navigate = useNavigate();
  return (
    <button
      onClick={() => navigate("/ai-assist")}
      className="fixed z-50 bottom-6 right-6 flex items-center gap-3 px-4 py-3 rounded-full bg-gradient-to-br from-luxury-navy to-luxury-gold shadow-lg hover:scale-105 transition-transform text-white font-semibold text-base"
      style={{
        boxShadow: "0 10px 32px 0 rgba(27,35,60,0.12)",
      }}
    >
      <Bot className="w-5 h-5" />
      AI Assistant
    </button>
  );
};
