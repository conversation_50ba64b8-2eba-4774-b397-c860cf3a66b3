
import React from "react";
import { <PERSON>, <PERSON><PERSON>eader, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Eye, Heart, Share2 } from "lucide-react";

const properties = [
  {
    id: 1,
    address: "1234 Ocean View Drive",
    city: "Manhattan Beach",
    price: 2850000,
    views: 156,
    inquiries: 12,
    status: "Hot",
    image: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?auto=format&fit=crop&w=300&h=200"
  },
  {
    id: 2,
    address: "5678 Sunset Boulevard", 
    city: "West Hollywood",
    price: 1650000,
    views: 89,
    inquiries: 7,
    status: "Active",
    image: "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?auto=format&fit=crop&w=300&h=200"
  },
  {
    id: 3,
    address: "910 Beverly Hills Drive",
    city: "Beverly Hills", 
    price: 4200000,
    views: 203,
    inquiries: 18,
    status: "Hot",
    image: "https://images.unsplash.com/photo-1600566753376-12c8ab7fb75b?auto=format&fit=crop&w=300&h=200"
  }
];

export const TopPerformingProperties: React.FC = () => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Hot": return "bg-red-100 text-red-800";
      case "Active": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <h3 className="text-lg font-semibold text-gray-900">Top Performing Properties</h3>
      </CardHeader>
      <CardContent className="space-y-4">
        {properties.map((property) => (
          <div key={property.id} className="flex gap-4 p-3 rounded-lg border hover:bg-gray-50 transition-colors">
            <img 
              src={property.image} 
              alt={property.address}
              className="w-20 h-16 object-cover rounded-lg"
            />
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-1">
                <div>
                  <p className="font-medium text-gray-900 text-sm truncate">
                    {property.address}
                  </p>
                  <p className="text-sm text-gray-600">{property.city}</p>
                </div>
                <Badge variant="secondary" className={`text-xs ${getStatusColor(property.status)}`}>
                  {property.status}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <p className="font-bold text-blue-600">
                  ${property.price.toLocaleString()}
                </p>
                <div className="flex items-center gap-3 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    {property.views}
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="h-3 w-3" />
                    {property.inquiries}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
