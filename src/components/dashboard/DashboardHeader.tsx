
import React from "react";
import { Calendar, MapPin } from "lucide-react";

export const DashboardHeader: React.FC = () => {
  const today = new Date().toLocaleDateString('en-US', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });

  return (
    <div className="bg-white border-b border-luxury-gold/20 px-6 py-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-luxury-navy">Good morning, <PERSON></h1>
          <p className="text-luxury-navy/60 flex items-center gap-2 mt-1">
            <Calendar className="w-4 h-4" />
            {today}
          </p>
        </div>
        <div className="text-right">
          <div className="text-luxury-gold font-medium text-lg">7 appointments today</div>
          <p className="text-luxury-navy/60 flex items-center justify-end gap-2">
            <MapPin className="w-4 h-4" />
            Los Angeles, CA
          </p>
        </div>
      </div>
    </div>
  );
};
