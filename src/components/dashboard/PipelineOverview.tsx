
import React from "react";
import { <PERSON>, <PERSON><PERSON>eader, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

const pipelineStages = [
  { name: "Lead", count: 12, value: 150000, color: "bg-gray-400" },
  { name: "Qualified", count: 8, value: 320000, color: "bg-blue-400" },
  { name: "Proposal", count: 5, value: 450000, color: "bg-yellow-400" },
  { name: "Negotiation", count: 3, value: 280000, color: "bg-orange-400" },
  { name: "Closing", count: 2, value: 180000, color: "bg-green-400" }
];

export const PipelineOverview: React.FC = () => {
  const totalValue = pipelineStages.reduce((sum, stage) => sum + stage.value, 0);

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Sales Pipeline</h3>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              ${(totalValue / 1000000).toFixed(1)}M
            </div>
            <div className="text-sm text-gray-500">Total Pipeline</div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {pipelineStages.map((stage, index) => (
          <div key={index} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${stage.color}`}></div>
                <span className="font-medium text-gray-900">{stage.name}</span>
                <span className="text-sm text-gray-500">({stage.count})</span>
              </div>
              <span className="font-semibold text-gray-900">
                ${(stage.value / 1000).toFixed(0)}K
              </span>
            </div>
            <Progress 
              value={(stage.value / totalValue) * 100} 
              className="h-2"
            />
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
