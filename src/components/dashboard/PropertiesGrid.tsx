
import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";

interface Property {
  id: number;
  address_street_complete?: string;
  address_city?: string;
  list_price?: number;
  photo_url?: string | null;
}

export const PropertiesGrid: React.FC = () => {
  const { data: properties, isLoading } = useQuery<Property[]>({
    queryKey: ["properties-grid"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("properties")
        .select("id, address_street_complete, address_city, list_price, photo_url")
        .order("created_at", { ascending: false })
        .limit(4);

      if (error) throw error;
      return data ?? [];
    },
  });

  return (
    <Card className="flex-1">
      <CardHeader className="pb-3">
        <h3 className="text-lg font-semibold text-luxury-navy">Recent Properties</h3>
      </CardHeader>
      <CardContent className="space-y-3">
        {isLoading ? (
          Array(3).fill(null).map((_, i) => (
            <div key={i} className="h-20 bg-gray-50 rounded-lg animate-pulse"></div>
          ))
        ) : (
          properties?.map((property) => (
            <div key={property.id} className="flex gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
              <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0 overflow-hidden">
                {property.photo_url ? (
                  <img src={property.photo_url} alt="" className="w-full h-full object-cover" />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-luxury-cream to-luxury-gold/20"></div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm text-luxury-navy truncate">
                  {property.address_street_complete}
                </div>
                <div className="text-xs text-gray-600 truncate">
                  {property.address_city}
                </div>
                <div className="text-sm font-semibold text-luxury-gold">
                  {property.list_price ? `$${Number(property.list_price).toLocaleString()}` : '—'}
                </div>
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
};
