
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { User, Home } from "lucide-react";

// Placeholder data for now; real query will require logic to match client wishes to properties.
const matches = [
  {
    client: { name: "<PERSON>", initials: "<PERSON>" },
    property: {
      address: "298 Aspen Ridge, Summit",
      price: 2950000,
      photo: "https://images.unsplash.com/photo-1523712999610-f77fbcfc3843?auto=format&fit=crop&w=400&q=80",
    },
  },
  {
    client: { name: "<PERSON>", initials: "<PERSON>" },
    property: {
      address: "1852 Oak Lane, Harbor",
      price: 2250000,
      photo: "https://images.unsplash.com/photo-1501854140801-50d01698950b?auto=format&fit=crop&w=400&q=80",
    },
  },
];

export const ClientPropertyMatches: React.FC = () => (
  <section>
    <h2 className="text-xl font-bold mb-4 text-luxury-navy">Client ↔ Property Matches</h2>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {matches.map((match, i) => (
        <Card key={i} className="rounded-xl overflow-hidden ring-1 ring-luxury-gold/10 bg-white/90 flex">
          <div className="w-28 h-36 bg-gray-100 flex items-center justify-center overflow-hidden">
            <img
              src={match.property.photo}
              alt=""
              className="h-full w-full object-cover"
            />
          </div>
          <CardContent className="flex flex-1 flex-col justify-center gap-3 p-4">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-luxury-navy/60" />
              <span className="font-semibold text-luxury-navy">{match.client.name}</span>
            </div>
            <div className="flex items-center gap-2">
              <Home className="w-4 h-4 text-luxury-gold" />
              <span className="text-luxury-navy">{match.property.address}</span>
            </div>
            <div className="font-bold text-lg text-luxury-gold">
              ${match.property.price.toLocaleString()}
            </div>
            <button
              className="primary-action mt-auto w-fit px-4 py-2 text-xs rounded-lg"
            >
              Match This Pair
            </button>
          </CardContent>
        </Card>
      ))}
    </div>
  </section>
);
