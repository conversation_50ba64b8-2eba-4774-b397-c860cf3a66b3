
import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { Mail, Phone, UserCheck } from "lucide-react";

interface Client {
  id: number;
  first_name: string;
  last_name: string;
  status?: string;
  last_contact_date?: string;
  next_follow_up_date?: string;
  email?: string;
  mobile_phone?: string;
}

export const ActiveClientsList: React.FC = () => {
  const { data, isLoading } = useQuery<Client[]>({
    queryKey: ["active-clients"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("clients")
        .select(
          "id, first_name, last_name, status, last_contact_date, next_follow_up_date, email, mobile_phone"
        )
        .eq("archived", false)
        .order("next_follow_up_date", { ascending: true })
        .limit(5);

      if (error) throw error;
      return data ?? [];
    },
  });

  return (
    <section>
      <h2 className="text-xl font-bold mb-4 text-luxury-navy">Active Clients</h2>
      <div className="space-y-3">
        {(isLoading ? Array(3).fill(null) : data).map((c, idx) =>
          c ? (
            <Card key={c.id} className="flex items-center rounded-xl ring-1 ring-luxury-gold/10 hover:ring-luxury-gold/40 transition">
              <CardContent className="flex items-center gap-3 p-3 w-full">
                <div className="h-11 w-11 bg-gradient-to-br from-luxury-navy/30 to-luxury-gold/20 rounded-full flex items-center justify-center text-xl font-bold text-luxury-navy">
                  {c.first_name[0]}
                  {c.last_name[0]}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-semibold text-luxury-navy truncate">{c.first_name} {c.last_name}</div>
                  <div className="text-xs text-luxury-navy/60 truncate">
                    {c.status ?? "Active"}
                  </div>
                  <div className="flex gap-2 mt-1 text-xs text-luxury-gold items-center">
                    <UserCheck className="w-4 h-4" />
                    <span>
                      Last contact:{" "}
                      {c.last_contact_date
                        ? new Date(c.last_contact_date).toLocaleDateString()
                        : "—"}
                    </span>
                  </div>
                  {c.next_follow_up_date && (
                    <div className="flex gap-2 text-xs text-blue-700 items-center mt-0.5">
                      <Phone className="w-4 h-4" />
                      Next action: {new Date(c.next_follow_up_date).toLocaleDateString()}
                    </div>
                  )}
                </div>
                <div className="flex flex-col gap-1">
                  {c.email && (
                    <a href={`mailto:${c.email}`} title="Email">
                      <Mail className="w-4 h-4 text-luxury-navy/60 hover:text-luxury-gold" />
                    </a>
                  )}
                  {c.mobile_phone && (
                    <a href={`tel:${c.mobile_phone}`} title="Call">
                      <Phone className="w-4 h-4 text-luxury-navy/60 hover:text-luxury-gold" />
                    </a>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card key={idx} className="h-16 animate-pulse rounded-xl bg-luxury-navy/5" />
          )
        )}
      </div>
    </section>
  );
};
