
import React from "react";
import { Card } from "@/components/ui/card";
import { TrendingUp, TrendingDown, DollarSign, Users, Home, Calendar } from "lucide-react";

const metrics = [
  {
    label: "Pipeline Value",
    value: "$1.2M",
    change: "+12%",
    trend: "up",
    icon: DollarSign,
    color: "text-green-600"
  },
  {
    label: "Active Clients",
    value: "47",
    change: "+3",
    trend: "up", 
    icon: Users,
    color: "text-blue-600"
  },
  {
    label: "Listed Properties",
    value: "23",
    change: "+5",
    trend: "up",
    icon: Home,
    color: "text-purple-600"
  },
  {
    label: "This Month Closed",
    value: "8",
    change: "-2",
    trend: "down",
    icon: Calendar,
    color: "text-orange-600"
  }
];

export const DashboardMetrics: React.FC = () => {
  return (
    <div className="grid grid-cols-4 gap-6">
      {metrics.map((metric, index) => (
        <Card key={index} className="p-6 hover:shadow-md transition-shadow">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-gray-600 font-medium">{metric.label}</p>
              <p className="text-3xl font-bold text-gray-900 mt-1">{metric.value}</p>
              <div className="flex items-center gap-1 mt-2">
                {metric.trend === "up" ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500" />
                )}
                <span className={`text-sm font-medium ${
                  metric.trend === "up" ? "text-green-600" : "text-red-600"
                }`}>
                  {metric.change}
                </span>
                <span className="text-sm text-gray-500">vs last month</span>
              </div>
            </div>
            <div className={`p-3 rounded-lg bg-gray-50`}>
              <metric.icon className={`h-6 w-6 ${metric.color}`} />
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};
