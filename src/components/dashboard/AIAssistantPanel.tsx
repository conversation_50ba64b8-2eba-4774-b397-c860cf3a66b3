
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send, Sparkles, Lightbulb, TrendingUp } from "lucide-react";

const quickActions = [
  { label: "Find similar properties", icon: TrendingUp },
  { label: "Schedule follow-ups", icon: Lightbulb },
  { label: "Generate market report", icon: Sparkles },
];

export const AIAssistantPanel: React.FC = () => {
  const [message, setMessage] = useState("");
  const [chatHistory, setChatHistory] = useState([
    {
      type: "assistant",
      message: "Hi <PERSON>! I noticed you have 3 clients ready for follow-up calls today. Would you like me to prioritize them by deal size?"
    }
  ]);

  const handleSend = () => {
    if (message.trim()) {
      setChatHistory(prev => [...prev, { type: "user", message }]);
      setMessage("");
      // Simulate AI response
      setTimeout(() => {
        setChatHistory(prev => [...prev, { 
          type: "assistant", 
          message: "I'll help you with that. Let me pull up the relevant information..." 
        }]);
      }, 1000);
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-blue-500" />
          <h3 className="text-lg font-semibold text-gray-900">AI Assistant</h3>
        </div>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col">
        {/* Quick Actions */}
        <div className="space-y-2 mb-4">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              className="w-full justify-start gap-2 text-left h-auto py-2"
            >
              <action.icon className="h-4 w-4" />
              {action.label}
            </Button>
          ))}
        </div>

        {/* Chat History */}
        <div className="flex-1 space-y-3 mb-4 overflow-y-auto">
          {chatHistory.map((chat, index) => (
            <div
              key={index}
              className={`p-3 rounded-lg ${
                chat.type === "user"
                  ? "bg-blue-500 text-white ml-4"
                  : "bg-gray-100 text-gray-900 mr-4"
              }`}
            >
              <p className="text-sm">{chat.message}</p>
            </div>
          ))}
        </div>

        {/* Input */}
        <div className="flex gap-2">
          <Input
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Ask me anything..."
            onKeyPress={(e) => e.key === 'Enter' && handleSend()}
            className="flex-1"
          />
          <Button onClick={handleSend} disabled={!message.trim()}>
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
