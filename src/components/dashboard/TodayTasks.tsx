
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Clock, MapPin, Phone } from "lucide-react";

const tasks = [
  { time: "10:00 AM", task: "Property showing", client: "<PERSON> Chen", location: "123 Oak St" },
  { time: "2:00 PM", task: "Contract review", client: "<PERSON>", location: "Office" },
  { time: "4:30 PM", task: "Follow-up call", client: "<PERSON>", location: "Call" },
];

export const TodayTasks: React.FC = () => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <h3 className="text-lg font-semibold text-luxury-navy">Today's Schedule</h3>
      </CardHeader>
      <CardContent className="space-y-3">
        {tasks.map((task, index) => (
          <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="text-sm font-medium text-luxury-navy min-w-[70px]">
              {task.time}
            </div>
            <div className="flex-1">
              <div className="font-medium text-sm">{task.task}</div>
              <div className="text-xs text-gray-600">{task.client}</div>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                {task.location === "Call" ? (
                  <Phone className="w-3 h-3" />
                ) : (
                  <MapPin className="w-3 h-3" />
                )}
                {task.location}
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
