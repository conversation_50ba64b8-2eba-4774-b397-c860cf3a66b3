
import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Send, Sparkles } from "lucide-react";

export const AIChat: React.FC = () => {
  const [message, setMessage] = useState("");

  const handleSend = () => {
    if (message.trim()) {
      console.log("Sending message:", message);
      setMessage("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <Card className="m-6 mt-0">
      <div className="flex items-center gap-3 p-4">
        <div className="flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-luxury-gold" />
          <span className="font-medium text-luxury-navy">Ask me anything</span>
        </div>
        <div className="flex-1 flex gap-3">
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Search clients, find properties, schedule appointments..."
            className="flex-1 resize-none border border-gray-200 rounded-lg px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-luxury-gold/20 focus:border-luxury-gold"
            rows={1}
          />
          <Button 
            onClick={handleSend}
            disabled={!message.trim()}
            className="bg-luxury-gold hover:bg-luxury-gold/90 text-white px-6"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
};
