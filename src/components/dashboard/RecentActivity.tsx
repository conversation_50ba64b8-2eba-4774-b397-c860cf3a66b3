
import React from "react";
import { <PERSON>, <PERSON><PERSON>eader, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Phone, Mail, Home, Calendar, FileText } from "lucide-react";

const activities = [
  {
    id: 1,
    type: "call",
    title: "Called Marcus Rodriguez",
    description: "Discussed pricing for downtown condo",
    time: "2 hours ago",
    icon: Phone,
    status: "completed"
  },
  {
    id: 2,
    type: "property",
    title: "New listing added",
    description: "4BR house on Maple Street - $850K",
    time: "4 hours ago",
    icon: Home,
    status: "new"
  },
  {
    id: 3,
    type: "meeting",
    title: "Property showing scheduled",
    description: "Emily Chen - Tomorrow 2:00 PM",
    time: "6 hours ago",
    icon: Calendar,
    status: "upcoming"
  },
  {
    id: 4,
    type: "document",
    title: "Contract signed",
    description: "Purchase agreement for 123 Oak St",
    time: "1 day ago",
    icon: FileText,
    status: "completed"
  },
  {
    id: 5,
    type: "email",
    title: "Follow-up email sent",
    description: "Market update to 15 clients",
    time: "2 days ago",
    icon: Mail,
    status: "completed"
  }
];

export const RecentActivity: React.FC = () => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "new": return "bg-blue-100 text-blue-800";
      case "upcoming": return "bg-orange-100 text-orange-800";
      case "completed": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
      </CardHeader>
      <CardContent className="space-y-4">
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="p-2 bg-gray-100 rounded-lg">
              <activity.icon className="h-4 w-4 text-gray-600" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <p className="font-medium text-gray-900 text-sm">{activity.title}</p>
                <Badge variant="secondary" className={`text-xs ${getStatusColor(activity.status)}`}>
                  {activity.status}
                </Badge>
              </div>
              <p className="text-sm text-gray-600">{activity.description}</p>
              <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
