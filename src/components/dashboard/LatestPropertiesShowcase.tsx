
import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";

interface Property {
  id: number;
  address_street_complete?: string;
  address_city?: string;
  address_zip_code?: string;
  list_price?: number;
  photo_url?: string | null;
  created_at?: string;
}

export const LatestPropertiesShowcase: React.FC = () => {
  const { data, isLoading } = useQuery<Property[]>({
    queryKey: ["latest-properties"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("properties")
        .select(
          "id, address_street_complete, address_city, address_zip_code, list_price, photo_url, created_at"
        )
        .order("created_at", { ascending: false })
        .limit(3);

      if (error) throw error;
      return data ?? [];
    },
  });

  return (
    <section>
      <h2 className="text-xl font-bold mb-4 text-luxury-navy">Latest Properties</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {(isLoading ? [1, 2, 3] : data).map((prop: any, idx: number) =>
          prop ? (
            <Card key={prop.id} className="rounded-2xl overflow-hidden group ring-1 ring-luxury-gold/10 hover:ring-luxury-gold/40 bg-white/80 shadow-lg transition">
              <div className="h-36 w-full bg-gradient-to-br from-luxury-cream to-luxury-navy/5 flex items-center justify-center overflow-hidden">
                {prop.photo_url ? (
                  <img src={prop.photo_url} alt="" className="h-full w-full object-cover" />
                ) : (
                  <div className="text-luxury-navy/30 text-3xl font-bold">No Image</div>
                )}
              </div>
              <CardContent className="py-4 flex flex-col gap-2">
                <div className="font-semibold text-luxury-navy truncate">
                  {prop.address_street_complete}, {prop.address_city}
                </div>
                <div className="text-luxury-navy/60 text-sm">
                  {prop.address_zip_code}
                </div>
                <div className="text-luxury-gold/90 font-bold text-lg">
                  {prop.list_price ? `$${Number(prop.list_price).toLocaleString()}` : "—"}
                </div>
                <div className="text-xs text-gray-500">{prop.created_at?.slice(0,10)}</div>
              </CardContent>
            </Card>
          ) : (
            <Card key={idx} className="animate-pulse h-44 bg-luxury-navy/5 flex" />
          )
        )}
      </div>
    </section>
  );
};
