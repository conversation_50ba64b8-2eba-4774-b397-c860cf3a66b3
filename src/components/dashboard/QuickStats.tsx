
import React from "react";
import { Card } from "@/components/ui/card";
import { TrendingUp, Users, Home, DollarSign } from "lucide-react";

const stats = [
  { label: "Active Clients", value: "18", icon: Users, color: "text-blue-600" },
  { label: "Listed Properties", value: "24", icon: Home, color: "text-green-600" },
  { label: "Pending Deals", value: "5", icon: TrendingUp, color: "text-orange-600" },
  { label: "Month Revenue", value: "$342K", icon: DollarSign, color: "text-luxury-gold" },
];

export const QuickStats: React.FC = () => {
  return (
    <div className="grid grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index} className="p-4 border border-luxury-gold/10">
          <div className="flex items-center gap-3">
            <stat.icon className={`w-5 h-5 ${stat.color}`} />
            <div>
              <div className="text-2xl font-bold text-luxury-navy">{stat.value}</div>
              <div className="text-sm text-luxury-navy/60">{stat.label}</div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};
