
import React from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardHeader, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { Phone, Mail, Eye } from "lucide-react";

interface Client {
  id: number;
  first_name: string;
  last_name: string;
  status?: string;
  last_contact_date?: string;
  next_follow_up_date?: string;
  email?: string;
  mobile_phone?: string;
  priority?: string;
}

export const ClientsTable: React.FC = () => {
  const { data: clients, isLoading } = useQuery<Client[]>({
    queryKey: ["clients-table"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("clients")
        .select("id, first_name, last_name, status, last_contact_date, next_follow_up_date, email, mobile_phone, priority")
        .eq("archived", false)
        .order("next_follow_up_date", { ascending: true })
        .limit(8);

      if (error) throw error;
      return data ?? [];
    },
  });

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'hot': return 'bg-red-100 text-red-800';
      case 'warm': return 'bg-orange-100 text-orange-800';
      case 'cold': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="flex-1 overflow-hidden">
      <CardHeader className="pb-3">
        <h2 className="text-lg font-semibold text-luxury-navy">Active Clients</h2>
      </CardHeader>
      <CardContent className="p-0 overflow-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Last Contact</TableHead>
              <TableHead>Next Action</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array(5).fill(null).map((_, i) => (
                <TableRow key={i}>
                  <TableCell colSpan={5} className="h-12 animate-pulse bg-gray-50"></TableCell>
                </TableRow>
              ))
            ) : (
              clients?.map((client) => (
                <TableRow key={client.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">
                    {client.first_name} {client.last_name}
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary" className={getStatusColor(client.status)}>
                      {client.status || 'Active'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sm text-gray-600">
                    {client.last_contact_date 
                      ? new Date(client.last_contact_date).toLocaleDateString()
                      : '—'
                    }
                  </TableCell>
                  <TableCell className="text-sm text-gray-600">
                    {client.next_follow_up_date 
                      ? new Date(client.next_follow_up_date).toLocaleDateString()
                      : '—'
                    }
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Eye className="w-4 h-4" />
                      </Button>
                      {client.mobile_phone && (
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Phone className="w-4 h-4" />
                        </Button>
                      )}
                      {client.email && (
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Mail className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
