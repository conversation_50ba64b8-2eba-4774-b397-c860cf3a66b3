
import React from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';

// The Unsplash image `photo-1470071459604-3b5ec3a7fe05` (foggy mountain summit)
const backgroundImage = "https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?auto=format&fit=crop&w=1500&q=80";

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div
      className="min-h-screen w-full bg-cover bg-center relative"
      style={{
        backgroundImage: `linear-gradient(to bottom right, rgba(248,246,240,0.92) 60%, rgba(26,40,82,0.25)), url('${backgroundImage}')`
      }}
    >
      <div className="flex h-screen bg-transparent">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Header />
          <main className="flex-1 overflow-y-auto bg-white/60 backdrop-blur-md">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
};

