'use client';

import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Building2, User, Settings, LogOut, Home, FileText, Calendar, HelpCircle, Send, Users, Search, Trophy, FileCode } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';

export const LuxurySidebar: React.FC = () => {
  const pathname = useLocation().pathname;
  const authState = useAuth();
  
  if (!authState) {
    console.error('useAuth must be used within AuthProvider');
    return null;
  }
  
  const { user, isAuthenticated, login, logout, loading } = authState;

  const navigationItems = [
    { href: '/', label: 'Home', icon: Home, badge: null },
    { href: '/dashboard', label: 'Dashboard', icon: Trophy, badge: null },
    { href: '/clients', label: 'Clients', icon: Users, badge: 18 },
    { href: '/properties', label: 'Properties', icon: Building2, badge: 8 },
    { href: '/documents', label: 'Documents', icon: FileText, badge: 3 },
    { href: '/schedule', label: 'Schedule', icon: Calendar, badge: null },
    { href: '/reports', label: 'Reports', icon: Calendar, badge: null },
    { href: '/settings', label: 'Settings', icon: Settings, badge: null },
    { href: '/dev-log', label: 'Dev Log', icon: FileCode, badge: null }, // All bugs fixed!
  ];

  const quickActions = [
    { label: 'Add Property', icon: Building2, href: '/properties' },
    { label: 'Find Leads', icon: Search, href: '/properties/search' },
    { label: 'Send Email', icon: Send, href: '/documents' },
  ];

  return (
    <aside className="h-screen w-72 bg-gradient-to-b from-slate-50/95 via-slate-100/95 to-slate-200/95 backdrop-blur-xl border-r border-white/50 shadow-2xl flex flex-col relative overflow-hidden">
      {/* Glassmorphism decorative overlay */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-48 -left-24 w-96 h-96 bg-gradient-to-br from-cyan-400/30 via-cyan-500/20 to-transparent rounded-full blur-3xl animate-float" />
        <div className="absolute bottom-0 -right-24 w-80 h-80 bg-gradient-to-tl from-blue-400/30 via-blue-500/20 to-transparent rounded-full blur-3xl animate-float-delayed" />
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-cyan-300/20 to-blue-300/20 rounded-full blur-2xl animate-pulse-slow" />
      </div>

      {/* Logo Section */}
      <div className="p-6 border-b border-white/20 relative z-10">
        <Link to="/" className="flex items-center space-x-3">
          <div className="relative">
            <div className="h-12 w-12 bg-white/30 backdrop-blur-xl border border-white/40 rounded-2xl flex items-center justify-center shadow-lg">
              <Building2 className="h-7 w-7 text-slate-700" />
            </div>
            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-gradient-to-r from-cyan-400 to-cyan-500 rounded-full animate-pulse" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-slate-800">Narissa</h1>
            <p className="text-xs text-slate-600">Realty CRM</p>
          </div>
        </Link>
      </div>

      {/* User Profile / Auth Section */}
      <div className="p-4 relative z-10">
        {isAuthenticated && user ? (
          <div className="glass-subtle rounded-2xl p-4 border border-white/30">
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 bg-gradient-to-br from-cyan-400/80 to-cyan-500/80 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/40 shadow-lg">
                <User className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold text-slate-800 truncate">{user.name}</p>
                <p className="text-xs text-slate-600">{user.role}</p>
              </div>
            </div>
            {user.metrics && (
              <div className="mt-4 pt-4 border-t border-white/20">
                <div className="grid grid-cols-3 gap-2 text-center">
                  <div>
                    <p className="text-lg font-bold text-slate-800">{user.metrics.activeListings}</p>
                    <p className="text-xs text-slate-600">Active</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-slate-800">{user.metrics.totalClients}</p>
                    <p className="text-xs text-slate-600">Clients</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-slate-800">{user.metrics.dealsThisMonth}</p>
                    <p className="text-xs text-slate-600">Deals</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            <div className="glass-subtle rounded-2xl p-4 border border-white/30">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 bg-slate-300/50 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/40">
                  <User className="h-5 w-5 text-slate-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-700">Sign in to access your CRM</p>
                </div>
              </div>
            </div>
            <Button 
              onClick={login}
              className="w-full bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white rounded-xl shadow-lg backdrop-blur-sm border border-white/20 transition-all duration-300 hover:scale-105"
              disabled={loading}
            >
              <User className="h-4 w-4 mr-2" />
              Login as Demo User
            </Button>
          </div>
        )}
      </div>

      {/* Achievement Badge */}
      {isAuthenticated && (
        <div className="p-4 relative z-10">
          <div className="glass-subtle rounded-2xl p-4 border border-white/30">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-orange-400/80 to-orange-500/80 backdrop-blur-sm rounded-xl border border-white/30">
                <Trophy className="h-4 w-4 text-white" />
              </div>
              <div>
                <p className="text-sm font-semibold text-slate-800">Top Performer</p>
                <p className="text-xs text-slate-600">March 2024</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="p-4 border-b border-white/20 relative z-10">
        <h3 className="text-sm font-semibold text-slate-700 mb-3 uppercase tracking-wide">Quick Actions</h3>
        <div className="space-y-3">
          {quickActions.map((action, index) => (
            <Link key={index} to={action.href}>
              <Button
                className="w-full justify-start glass-subtle hover:glass-strong text-slate-700 hover:text-slate-800 transition-all duration-300 rounded-xl shadow-sm hover:shadow-md hover:scale-105 border border-white/30 hover:border-white/40"
                size="sm"
              >
                <action.icon className="h-4 w-4 mr-3" />
                {action.label}
              </Button>
            </Link>
          ))}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 relative z-10">
        <div className="space-y-2">
          {navigationItems.map((item, index) => {
            const isActive = pathname === item.href;
            return (
              <Link key={index} to={item.href}>
                <Button
                  variant={isActive ? "default" : "ghost"}
                  className={`w-full justify-start transition-all duration-300 rounded-xl group ${
                    isActive 
                      ? 'bg-gradient-to-r from-cyan-500 to-cyan-600 text-white shadow-lg scale-105 border border-white/30' 
                      : 'hover:glass-subtle text-slate-700 hover:text-slate-800 hover:scale-105 border border-transparent hover:border-white/30'
                  }`}
                >
                  <div className={`p-2 ${isActive ? 'bg-white/20' : 'bg-white/10 group-hover:bg-white/20'} backdrop-blur-sm border border-white/30 rounded-lg mr-3 group-hover:scale-110 transition-transform`}>
                    <item.icon className="h-4 w-4" />
                  </div>
                  <span className="font-medium">{item.label}</span>
                  {item.badge && (
                    <span className={`ml-auto px-2 py-1 text-xs rounded-full ${
                      isActive 
                        ? 'bg-white/20 text-white' 
                        : 'bg-gradient-to-r from-orange-400/80 to-orange-500/80 text-white border border-white/30'
                    }`}>
                      {item.badge}
                    </span>
                  )}
                </Button>
              </Link>
            );
          })}
        </div>
      </nav>

      {/* Settings & Auth Actions */}
      <div className="p-4 border-t border-white/20 relative z-10 space-y-2">
        <Link to="/settings">
          <Button variant="ghost" className="w-full justify-start text-slate-600 hover:text-slate-800 hover:glass-subtle rounded-xl group hover:scale-105 transition-all duration-300 border border-transparent hover:border-white/30">
            <div className="p-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-lg mr-3 group-hover:scale-110 transition-transform">
              <Settings className="h-4 w-4" />
            </div>
            <span className="font-medium">Settings</span>
          </Button>
        </Link>
        
        <Link to="/ai-assist">
          <Button variant="ghost" className="w-full justify-start text-slate-600 hover:text-slate-800 hover:glass-subtle rounded-xl group hover:scale-105 transition-all duration-300 border border-transparent hover:border-white/30">
            <div className="p-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-lg mr-3 group-hover:scale-110 transition-transform">
              <HelpCircle className="h-4 w-4" />
            </div>
            <span className="font-medium">Help</span>
          </Button>
        </Link>
        
        {isAuthenticated && (
          <Button 
            onClick={logout}
            variant="ghost" 
            className="w-full justify-start text-slate-600 hover:text-red-600 hover:glass-subtle rounded-xl group hover:scale-105 transition-all duration-300 border border-transparent hover:border-red-200/30"
          >
            <div className="p-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-lg mr-3 group-hover:scale-110 transition-transform group-hover:bg-red-100/30">
              <LogOut className="h-4 w-4" />
            </div>
            <span className="font-medium">Sign Out</span>
          </Button>
        )}
      </div>
    </aside>
  );
};