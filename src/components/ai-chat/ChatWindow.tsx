
import React, { useRef } from "react";
import { Paperclip, Send, User, Bo<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

// Placeholder messages for layout
const placeholderMessages = [
  {
    id: 1,
    role: "assistant",
    name: "AI Assistant",
    content: "Hello! How can I help you with your real estate questions today?",
    avatar: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=100&q=80"
  },
  {
    id: 2,
    role: "user",
    name: "<PERSON>",
    content: "Can you analyze the market trends for zip 90210?",
    avatar: null
  },
  {
    id: 3,
    role: "assistant",
    name: "AI Assistant",
    content: "Absolutely! Upload any documents or add details for a more in-depth analysis.",
    avatar: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=100&q=80"
  }
];

export const ChatWindow = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  return (
    <main className="flex-1 flex flex-col h-full bg-white/70">
      {/* Header */}
      <div className="flex-none h-16 flex items-center px-8 border-b border-luxury-gold/10 bg-white/80 shadow-sm">
        <div className="flex items-center gap-4">
          <Bot className="h-8 w-8 text-luxury-navy bg-luxury-cream rounded-full p-1 shadow" />
          <div>
            <div className="font-semibold text-lg text-luxury-navy">AI Chat Assistant</div>
            <div className="text-xs text-luxury-navy/50">Empowered by AI</div>
          </div>
        </div>
      </div>

      {/* Chat messages */}
      <section className="flex-1 px-0 sm:px-2 py-4 flex flex-col gap-2 overflow-y-auto">
        <div className="max-w-2xl mx-auto w-full flex flex-col gap-4">
          {placeholderMessages.map((msg) => (
            <div
              key={msg.id}
              className={`flex gap-3 items-end ${msg.role === "user" ? "justify-end" : "justify-start"}`}
            >
              {msg.role === "assistant" && (
                <img
                  src={msg.avatar || "/placeholder.svg"}
                  alt={msg.name}
                  className="h-10 w-10 rounded-full bg-luxury-cream border border-luxury-gold shadow"
                />
              )}
              <div
                className={`rounded-xl px-4 py-2 max-w-lg shadow ${
                  msg.role === "assistant"
                    ? "bg-luxury-cream text-luxury-navy border border-luxury-gold/30"
                    : "bg-luxury-navy text-white"
                } break-words`}
              >
                <div className="text-sm">{msg.content}</div>
              </div>
              {msg.role === "user" && (
                <div className="h-10 w-10 rounded-full flex items-center justify-center bg-luxury-navy shadow">
                  <User className="h-5 w-5 text-white" />
                </div>
              )}
            </div>
          ))}
        </div>
      </section>

      {/* Input area */}
      <form className="flex-none border-t border-luxury-gold/10 bg-white/90 px-6 py-4 flex items-end gap-3" onSubmit={e => e.preventDefault()}>
        <Button
          type="button"
          variant="ghost"
          className="rounded-lg"
          onClick={() => fileInputRef.current?.click()}
        >
          <Paperclip className="h-6 w-6 text-luxury-navy/60" />
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            multiple
            // onChange={handleFileUpload} // Hook up later
          />
        </Button>
        <Input
          className="flex-1 rounded-lg bg-white/90 border border-luxury-gold/20 text-base"
          placeholder="Send a message..."
          // value={message}
          // onChange={e => setMessage(e.target.value)}
        />
        <Button type="submit" className="rounded-lg px-4 py-2 bg-luxury-navy text-white hover:bg-luxury-navy-light">
          <Send className="h-5 w-5" />
        </Button>
      </form>
    </main>
  );
};
