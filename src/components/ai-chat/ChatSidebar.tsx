
import React from "react";
import { FilePlus, MessageCircle, <PERSON><PERSON><PERSON>, User, PlusCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

const chats = [
  { id: 1, title: "Market Report", icon: MessageCircle },
  { id: 2, title: "Ask About a Property", icon: MessageCircle },
  { id: 3, title: "Contract Questions", icon: MessageCircle }
];

export const ChatSidebar = () => (
  <aside className="flex flex-col w-72 max-w-xs h-full bg-white/75 shadow-xl border-r border-luxury-gold/20 z-20">
    <div className="flex-none px-4 py-5 border-b border-luxury-gold/10">
      <Button className="w-full rounded-lg flex gap-2">
        <PlusCircle className="h-5 w-5" />
        New Chat
      </Button>
    </div>
    <nav className="flex-1 px-2 py-4 overflow-y-auto space-y-1">
      {chats.map((chat) => (
        <button
          key={chat.id}
          className="w-full flex items-center gap-3 px-3 py-2 rounded-lg transition hover:bg-blue-100/60 text-left text-luxury-navy/90 focus:outline-none"
        >
          <chat.icon className="h-5 w-5" />
          <span className="text-sm truncate">{chat.title}</span>
        </button>
      ))}
    </nav>
    <div className="p-4 border-t border-luxury-gold/10 flex items-center gap-3">
      <User className="h-8 w-8 text-luxury-navy bg-gradient-to-br from-luxury-navy/20 to-luxury-gold/10 rounded-full p-1" />
      <div className="flex-1">
        <div className="text-xs font-semibold text-luxury-navy">Sarah Johnson</div>
        <div className="text-[10px] text-luxury-navy/50">Agent Account</div>
      </div>
      <Settings className="h-5 w-5 text-luxury-navy/40 cursor-pointer hover:text-luxury-navy" />
    </div>
  </aside>
);
