
import React, { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getMockClients } from "@/lib/mockData";
import { clientDataEvents } from "@/utils/dataEvents";
import { ChevronDown, ChevronUp, Eye } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
} from "@/components/ui/table";

async function fetchClients() {
  // Simulate API delay for realistic loading state
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Get all mock clients and sort by created_at descending
  const clients = getMockClients();
  return clients
    .sort((a, b) => {
      const dateA = new Date(a.created_at || 0).getTime();
      const dateB = new Date(b.created_at || 0).getTime();
      return dateB - dateA;
    })
    .slice(0, 25);
}

export function ClientList() {
  const navigate = useNavigate();
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());
  const queryClient = useQueryClient();
  
  // Listen for client data changes and invalidate cache
  useEffect(() => {
    const unsubscribe = clientDataEvents.addListener(() => {
      queryClient.invalidateQueries({ queryKey: ['clients'] });
    });
    return unsubscribe;
  }, [queryClient]);
  
  const { data, isLoading, error } = useQuery({
    queryKey: ["clients"],
    queryFn: fetchClients,
  });

  const toggleRow = (clientId: number) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(clientId)) {
      newExpanded.delete(clientId);
    } else {
      newExpanded.add(clientId);
    }
    setExpandedRows(newExpanded);
  };

  if (isLoading) return <div className="text-center py-8">Loading clients...</div>;
  if (error) return <div className="text-center py-8 text-red-500">Error loading clients.</div>;
  if (!data || data.length === 0) return <div className="text-center py-8 text-muted-foreground">No clients found.</div>;

  return (
    <div className="bg-white/40 backdrop-blur-xl border border-white/30 rounded-3xl shadow-2xl p-6">
      <Table>
        <TableCaption className="text-slate-600">Showing {data.length} of {getMockClients().length} total clients</TableCaption>
        <TableHeader>
          <TableRow className="border-b border-white/20">
            <TableHead className="text-slate-700 font-semibold w-8"></TableHead>
            <TableHead className="text-slate-700 font-semibold">Name</TableHead>
            <TableHead className="text-slate-700 font-semibold">Email</TableHead>
            <TableHead className="text-slate-700 font-semibold">Phone</TableHead>
            <TableHead className="text-slate-700 font-semibold">Type</TableHead>
            <TableHead className="text-slate-700 font-semibold">Status</TableHead>
            <TableHead className="text-slate-700 font-semibold text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((c: any) => {
            const isExpanded = expandedRows.has(c.id);
            return (
              <React.Fragment key={c.id}>
                <TableRow 
                  className="border-b border-white/10 hover:bg-white/10 transition-colors cursor-pointer"
                  onClick={() => toggleRow(c.id)}
                >
                  <TableCell>
                    {isExpanded ? (
                      <ChevronUp className="h-4 w-4 text-slate-600" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-slate-600" />
                    )}
                  </TableCell>
                  <TableCell className="font-medium text-slate-800">{c.first_name} {c.last_name}</TableCell>
                  <TableCell className="text-slate-600">{c.email ?? "—"}</TableCell>
                  <TableCell className="text-slate-600">{c.mobile_phone ?? "—"}</TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      c.client_type === 'buyer' ? 'bg-blue-100 text-blue-800' :
                      c.client_type === 'seller' ? 'bg-green-100 text-green-800' :
                      c.client_type === 'investor' ? 'bg-purple-100 text-purple-800' :
                      'bg-orange-100 text-orange-800'
                    }`}>
                      {c.client_type}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      c.status === 'active' ? 'bg-emerald-100 text-emerald-800' :
                      c.status === 'qualified' ? 'bg-cyan-100 text-cyan-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {c.status}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/clients/${c.id}`);
                      }}
                      className="hover:bg-white/20"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
                {isExpanded && (
                  <TableRow>
                    <TableCell colSpan={7} className="bg-white/5 p-0">
                      <div className="p-6 space-y-4">
                        <div className="grid grid-cols-3 gap-6">
                          <div>
                            <h4 className="text-sm font-semibold text-slate-700 mb-2">Financial Profile</h4>
                            <div className="space-y-1 text-sm">
                              <p><span className="text-slate-600">Budget:</span> ${parseInt(c.price_range_min || 0).toLocaleString()} - ${parseInt(c.price_range_max || 0).toLocaleString()}</p>
                              <p><span className="text-slate-600">Pre-approved:</span> ${parseInt(c.pre_approval_amount || 0).toLocaleString()}</p>
                              <p><span className="text-slate-600">Lender:</span> {c.lender_name || "Not specified"}</p>
                            </div>
                          </div>
                          <div>
                            <h4 className="text-sm font-semibold text-slate-700 mb-2">Preferences</h4>
                            <div className="space-y-1 text-sm">
                              <p><span className="text-slate-600">Property Type:</span> {c.property_type_preference || "Any"}</p>
                              <p><span className="text-slate-600">Min Bedrooms:</span> {c.min_bedrooms || "Not specified"}</p>
                              <p><span className="text-slate-600">Timeline:</span> {c.timeline_to_buy || "Flexible"}</p>
                              <p><span className="text-slate-600">Motivation:</span> {c.motivation_level || "Unknown"}</p>
                            </div>
                          </div>
                          <div>
                            <h4 className="text-sm font-semibold text-slate-700 mb-2">Contact Info</h4>
                            <div className="space-y-1 text-sm">
                              <p><span className="text-slate-600">Stage:</span> {c.stage}</p>
                              <p><span className="text-slate-600">Priority:</span> <span className={`font-medium ${c.priority === 'high' ? 'text-red-600' : 'text-slate-700'}`}>{c.priority}</span></p>
                              <p><span className="text-slate-600">Rating:</span> {'⭐'.repeat(c.rating || 0)}</p>
                              <p><span className="text-slate-600">Last Contact:</span> {c.last_contact_date ? new Date(c.last_contact_date).toLocaleDateString() : "Never"}</p>
                            </div>
                          </div>
                        </div>
                        {(() => {
                          // Check localStorage first, then fall back to mock data
                          const savedNotes = localStorage.getItem(`client_notes_${c.id}`);
                          const displayNotes = savedNotes !== null ? savedNotes : c.notes;
                          
                          return displayNotes ? (
                            <div className="mt-4 p-4 bg-slate-50/50 rounded-lg">
                              <h4 className="text-sm font-semibold text-slate-700 mb-1">Notes</h4>
                              <p className="text-sm text-slate-600">{displayNotes}</p>
                            </div>
                          ) : null;
                        })()}
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </React.Fragment>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
