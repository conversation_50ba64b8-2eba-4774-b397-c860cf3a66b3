
import React from 'react';
import { Button } from '@/components/ui/button';
import { Users, Home, Calendar, Sparkles, FileText, TrendingUp } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { AIHomepageChat } from './AIHomepageChat';

export const AILandingPage: React.FC = () => {
  const navigate = useNavigate();

  const quickActions = [
    {
      label: "Clients",
      icon: Users,
      action: () => navigate('/clients'),
      description: "View and manage your clients"
    },
    {
      label: "Properties",
      icon: Home, 
      action: () => navigate('/properties'),
      description: "Browse your property listings"
    },
    {
      label: "Schedule",
      icon: Calendar,
      action: () => navigate('/schedule'),
      description: "Check your appointments"
    },
    {
      label: "Documents",
      icon: FileText,
      action: () => navigate('/documents'),
      description: "Access your forms"
    },
    {
      label: "Reports",
      icon: TrendingUp,
      action: () => navigate('/reports'),
      description: "View analytics"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-6 -mt-16 bg-gradient-to-br from-slate-50 via-cyan-50 to-orange-50">
      {/* Glassmorphism background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-cyan-200/30 to-cyan-400/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-br from-orange-200/30 to-orange-400/20 rounded-full blur-3xl"></div>
      </div>

      {/* Main Content */}
      <div className="w-full max-w-5xl mx-auto space-y-8 relative z-10">
        
        {/* Welcome Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-white/30 backdrop-blur-xl border border-white/20 rounded-2xl shadow-lg">
              <Sparkles className="h-8 w-8 text-cyan-600" />
            </div>
            <h1 className="text-3xl font-bold text-slate-800">Narissa Realty CRM</h1>
          </div>
          <p className="text-lg text-slate-600">
            Your AI-Powered Real Estate Assistant
          </p>
        </div>

        {/* AI Chat Interface - Now the Primary Feature */}
        <AIHomepageChat />

        {/* Quick Actions - Now Secondary */}
        <div className="text-center space-y-4">
          <p className="text-sm text-slate-500">Or jump directly to:</p>
          <div className="grid grid-cols-5 gap-4 max-w-3xl mx-auto">
            {quickActions.map((action, index) => (
              <Button
                key={index}
                variant="outline"
                onClick={action.action}
                className="h-auto p-4 flex flex-col items-center gap-2 hover:shadow-xl transition-all duration-300 bg-white/30 backdrop-blur-xl border border-white/40 hover:border-white/60 rounded-2xl hover:bg-white/40 hover:scale-105"
              >
                <div className="p-2 bg-gradient-to-br from-white/40 to-white/20 backdrop-blur-sm rounded-xl border border-white/30">
                  <action.icon className="h-5 w-5 text-slate-700" />
                </div>
                <div className="text-center">
                  <div className="font-medium text-sm text-slate-800">{action.label}</div>
                  <div className="text-xs text-slate-600 mt-0.5">{action.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
