import React, { useState, useRef, useEffect } from 'react';
import { Send, Sparkles, User, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { aiService, type AIMessage } from '@/services/aiService';
import { realClientsData } from '@/data/realClientsData';
import { realPropertiesData } from '@/data/realPropertiesData';
import { useNavigate } from 'react-router-dom';
import { AnimatedSuggestions } from './AnimatedSuggestions';
import { MessageRenderer } from '@/components/chat/MessageRenderer';
import { chatStorage } from '@/utils/chatStorage';

export const AIHomepageChat: React.FC = () => {
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugMode, setDebugMode] = useState(false); // Add debug mode
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const handleSuggestionClick = (suggestion: string) => {
    // Add the suggestion as a user message and send it
    const userMessage: AIMessage = {
      role: 'user',
      content: suggestion,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    
    // Then process it as if the user typed it
    handleSendMessageWithText(suggestion);
  };

  // Initialize with stored messages or greeting
  useEffect(() => {
    const storedMessages = chatStorage.loadMessages();
    if (storedMessages.length > 0) {
      setMessages(storedMessages);
    } else {
      const welcomeMessage: AIMessage = {
        role: 'assistant',
        content: `Hello, Narissa! I'm your AI assistant with access to ${realClientsData.length} clients and ${realPropertiesData.length} properties.`,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, []);

  // Save messages to storage whenever they change
  useEffect(() => {
    if (messages.length > 0) {
      chatStorage.saveMessages(messages);
    }
  }, [messages]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [messages]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const handleSendMessageWithText = async (text: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await aiService.sendMessage(text, messages);
      
      const aiMessage: AIMessage = {
        role: 'assistant',
        content: response.message,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, aiMessage]);
      
      // Show remaining requests warning
      const remaining = aiService.getRemainingRequests();
      if (remaining < 5) {
        setError(`Low on requests: ${remaining} remaining this hour`);
      }
    } catch (err) {
      console.error('AI Chat error:', err);
      setError('Failed to get response. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: AIMessage = {
      role: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    
    handleSendMessageWithText(inputValue);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Chat Container */}
      <div className="bg-white/40 backdrop-blur-xl border border-white/30 rounded-3xl shadow-2xl overflow-hidden">
        {/* Messages Area */}
        <ScrollArea ref={scrollAreaRef} className="h-[400px] p-6">
          <div className="space-y-4">
            {/* Show animated suggestions only when there's 1 message (the greeting) */}
            {messages.length === 1 && (
              <div className="mb-8">
                <AnimatedSuggestions onSuggestionClick={handleSuggestionClick} />
              </div>
            )}
            
            {messages.map((message, index) => (
              <div
                key={index}
                className={`flex gap-3 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.role === 'assistant' && (
                  <div className="h-8 w-8 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <Sparkles className="h-4 w-4 text-white" />
                  </div>
                )}
                <div
                  className={`max-w-[80%] p-4 rounded-2xl ${
                    message.role === 'user'
                      ? 'bg-slate-100 text-slate-800'
                      : 'bg-gradient-to-r from-cyan-50 to-cyan-100/50 text-slate-800 border border-cyan-200'
                  }`}
                >
                  <MessageRenderer content={message.content} className="text-sm" />
                  {debugMode && message.role === 'assistant' && (
                    <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
                      <strong>Raw AI Response:</strong>
                      <pre className="whitespace-pre-wrap">{message.content}</pre>
                    </div>
                  )}
                  <p className="text-xs text-slate-500 mt-2">
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
                {message.role === 'user' && (
                  <div className="h-8 w-8 bg-slate-200 rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="h-4 w-4 text-slate-600" />
                  </div>
                )}
              </div>
            ))}
            
            {isLoading && (
              <div className="flex gap-3 justify-start">
                <div className="h-8 w-8 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-full flex items-center justify-center">
                  <Sparkles className="h-4 w-4 text-white animate-pulse" />
                </div>
                <div className="bg-gradient-to-r from-cyan-50 to-cyan-100/50 p-4 rounded-2xl border border-cyan-200">
                  <div className="flex gap-1">
                    <div className="h-2 w-2 bg-cyan-600 rounded-full animate-bounce" />
                    <div className="h-2 w-2 bg-cyan-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="h-2 w-2 bg-cyan-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Error message */}
        {error && (
          <div className="px-6 py-3 bg-orange-50 border-t border-orange-200">
            <div className="flex items-center gap-2 text-sm text-orange-700">
              <AlertCircle className="h-4 w-4" />
              {error}
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="border-t border-white/30 bg-white/20 p-6">
          <div className="flex justify-between items-center mb-3">
            <p className="text-xs text-slate-500">
              {aiService.getRemainingRequests()} AI requests remaining • Powered by Gemini 2.5 Flash
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => {
                  chatStorage.clearMessages();
                  const welcomeMessage: AIMessage = {
                    role: 'assistant',
                    content: `Hello, Narissa! I'm your AI assistant with access to ${realClientsData.length} clients and ${realPropertiesData.length} properties.`,
                    timestamp: new Date()
                  };
                  setMessages([welcomeMessage]);
                }}
                className="text-xs px-3 py-1 bg-red-200 hover:bg-red-300 rounded"
                title="Clear conversation history"
              >
                Clear
              </button>
              <button
                onClick={() => setDebugMode(!debugMode)}
                className="text-xs px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded"
              >
                Debug: {debugMode ? 'ON' : 'OFF'}
              </button>
            </div>
          </div>
          <div className="flex gap-4">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about your clients, properties, or schedule..."
              disabled={isLoading}
              className="flex-1 h-14 text-lg border-0 focus:ring-0 focus:border-0 bg-white/50 backdrop-blur-sm rounded-2xl px-6 placeholder:text-slate-500"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              className="h-14 px-8 bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white rounded-2xl font-medium shadow-lg backdrop-blur-sm border border-white/20 disabled:opacity-50"
            >
              <Send className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};