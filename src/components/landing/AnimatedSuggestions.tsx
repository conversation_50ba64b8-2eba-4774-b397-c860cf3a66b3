import React, { useState, useEffect } from 'react';

interface AnimatedSuggestionsProps {
  onSuggestionClick: (suggestion: string) => void;
}

// Expanded list of rotating suggestions showcasing AI capabilities
const suggestions = [
  {
    text: "Show me all active buyers",
    icon: "🏠",
    category: "search"
  },
  {
    text: "Schedule a viewing for tomorrow at 2pm",
    icon: "📅",
    category: "schedule"
  },
  {
    text: "Find properties under $500k in Davis",
    icon: "💰",
    category: "search"
  },
  {
    text: "Create new client record for <PERSON>",
    icon: "👤",
    category: "create"
  },
  {
    text: "Show recent appointments with <PERSON>",
    icon: "📋",
    category: "search"
  },
  {
    text: "Find cash buyers looking for investment properties",
    icon: "💵",
    category: "search"
  },
  {
    text: "Update budget for <PERSON> to $750k",
    icon: "✏️",
    category: "update"
  },
  {
    text: "Show properties with 4+ bedrooms",
    icon: "🛏️",
    category: "search"
  },
  {
    text: "Send follow-up email to pending clients",
    icon: "📧",
    category: "action"
  },
  {
    text: "Generate market analysis for Oak Street area",
    icon: "📊",
    category: "report"
  }
];

export const AnimatedSuggestions: React.FC<AnimatedSuggestionsProps> = ({ onSuggestionClick }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isPaused) return;

    const interval = setInterval(() => {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentIndex((prev) => (prev + 1) % suggestions.length);
        setIsAnimating(false);
      }, 300);
    }, 5000); // Change every 5 seconds

    return () => clearInterval(interval);
  }, [isPaused]);

  const currentSuggestion = suggestions[currentIndex];

  return (
    <div className="relative h-16 flex items-center justify-center">
      <button
        onClick={() => onSuggestionClick(currentSuggestion.text)}
        onMouseEnter={() => setIsPaused(true)}
        onMouseLeave={() => setIsPaused(false)}
        className={`flex items-center gap-3 px-6 py-3 bg-white/50 hover:bg-white/70 backdrop-blur-sm border border-white/40 rounded-full hover:shadow-lg hover:scale-105 cursor-pointer transition-all duration-300 ${
          isAnimating ? 'opacity-0 translate-y-2' : 'opacity-100 translate-y-0'
        }`}
      >
        <span className="text-2xl">{currentSuggestion.icon}</span>
        <span className="text-sm font-medium text-slate-700">
          {currentSuggestion.text}
        </span>
      </button>

      {/* Progress indicators */}
      <div className="absolute -bottom-6 flex gap-1">
        {suggestions.map((_, index) => (
          <div
            key={index}
            className={`h-1 transition-all duration-300 rounded-full ${
              index === currentIndex
                ? 'w-6 bg-cyan-500'
                : 'w-1 bg-slate-300'
            }`}
          />
        ))}
      </div>
    </div>
  );
};