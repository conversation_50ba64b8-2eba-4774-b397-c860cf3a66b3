
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home,
  Users, 
  Building2,
  Calendar, // Allowed "calendar" icon per allowed list
  FileText,
  BarChart3,
  Settings,
  Plus,
  Search,
  Phone
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Add new AI Chat icon using allowed "calendar-clock" as a stand-in (no chat icon in allowed set)
import { CalendarClock } from 'lucide-react';

const mainNavItems = [
  { icon: Home, label: 'Dashboard', href: '/', badge: null, important: false },
  { icon: Users, label: 'Clients', href: '/clients', badge: '18', important: true },
  { icon: Building2, label: 'Properties', href: '/properties', badge: '24', important: true },
  { icon: Calendar, label: 'Schedule', href: '/schedule', badge: '3', important: false },
  { icon: FileText, label: 'Documents', href: '/documents', badge: null, important: false },
  // New Buttons
  { icon: Calendar, label: 'Calendar', href: '/calendar', badge: null, important: false }, // new Calendar button
  { icon: BarChart3, label: 'Marketing', href: '/marketing', badge: null, important: false }, // new Marketing button
  { icon: CalendarClock, label: 'AI Chat Assist', href: '/ai-assist', badge: null, important: false }, // new Chat Assist button
];

const secondaryNavItems = [
  { icon: BarChart3, label: 'Reports', href: '/reports' },
  { icon: Settings, label: 'Settings', href: '/settings' },
];

export const Sidebar: React.FC = () => {
  const location = useLocation();

  return (
    <aside className="w-72 bg-white/60 backdrop-blur-2xl border-r border-blue-100 flex flex-col h-full">
      {/* Logo */}
      <div className="p-6 border-b border-blue-100">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-primary rounded-lg flex items-center justify-center shadow-lg">
            <Building2 className="h-6 w-6 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-foreground">Narissa</h1>
            <p className="text-sm text-muted-foreground">Real Estate CRM</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-b border-blue-100">
        <div className="space-y-2">
          <Button asChild className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-medium shadow-md">
            <Link to="/clients/new">
              <Plus className="h-4 w-4 mr-2" />
              Add Client
            </Link>
          </Button>
          <Button asChild className="w-full bg-blue-100 hover:bg-blue-200 text-primary font-medium">
            <Link to="/properties/search">
              <Search className="h-4 w-4 mr-2" />
              Find Property
            </Link>
          </Button>
        </div>
      </div>

      {/* Main Navigation */}
      <nav className="flex-1 p-4 overflow-y-auto">
        <div className="space-y-1">
          <p className="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">
            Main Menu
          </p>
          {mainNavItems.map((item, index) => {
            const isActive = location.pathname === item.href;
            return (
            <Button
              key={index}
              variant="ghost"
              className={`w-full justify-start text-left font-medium text-base h-11 ${
                isActive
                  ? 'bg-white/20 text-foreground' 
                  : 'text-muted-foreground hover:text-foreground hover:bg-white/10'
              }`}
              asChild
            >
              <Link to={item.href}>
                <item.icon className="h-5 w-5 mr-3" />
                <span className="flex-1">{item.label}</span>
                {item.badge && (
                  <Badge 
                    variant={item.important ? "destructive" : "default"}
                    className="text-xs px-2 py-0.5"
                  >
                    {item.badge}
                  </Badge>
                )}
              </Link>
            </Button>
          )})}
        </div>

        {/* Secondary Navigation */}
        <div className="mt-8 space-y-1">
          <p className="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">
            Tools
          </p>
          {secondaryNavItems.map((item, index) => {
            const isActive = location.pathname === item.href;
            return (
            <Button 
              key={index}
              variant="ghost"
              className={`w-full justify-start font-medium text-base h-11 ${
                isActive
                  ? 'bg-white/20 text-foreground' 
                  : 'text-muted-foreground hover:text-foreground hover:bg-white/10'
              }`}
              asChild
            >
              <Link to={item.href}>
                <item.icon className="h-5 w-5 mr-3" />
                {item.label}
              </Link>
            </Button>
          )})}
        </div>
      </nav>

      {/* Emergency Contact */}
      <div className="p-4 border-t border-blue-100 mt-auto">
        <Button className="w-full bg-red-50 hover:bg-red-100 text-destructive font-medium border border-red-100">
          <Phone className="h-4 w-4 mr-2" />
          Emergency Contact
        </Button>
      </div>
    </aside>
  );
};

