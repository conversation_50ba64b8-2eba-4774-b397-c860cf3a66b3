import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { mockDashboardFunctions } from '@/lib/mockData';
import { formatDistanceToNow } from 'date-fns';
import { Link } from 'react-router-dom';
import { Line, LineChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { 
  Users,
  Building2,
  Calendar,
  DollarSign,
  CheckCircle,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Checkbox } from "@/components/ui/checkbox";
import { 
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from "@/components/ui/chart";

const fetchDashboardStats = async () => {
  // Using mockData service instead of Supabase
  return mockDashboardFunctions.getDashboardStats();
};

const fetchUrgentTasks = async () => {
  // Using mockData service instead of Supabase
  return mockDashboardFunctions.getUrgentTasks();
};

export const Dashboard: React.FC = () => {
  const { data: stats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['dashboardStats'],
    queryFn: fetchDashboardStats
  });
  const { data: urgentTasksData, isLoading: isLoadingTasks } = useQuery({
    queryKey: ['urgentTasks'],
    queryFn: fetchUrgentTasks
  });

  const salesStats = [
    { label: "Total Sales", value: "$45,231.89", icon: DollarSign, change: "+20.1%", changeType: "positive" },
    { label: "Total Customers", value: isLoadingStats ? "..." : stats?.newLeads ?? 0, icon: Users, change: "+180.1%", changeType: "positive" },
    { label: "Total Properties", value: isLoadingStats ? "..." : stats?.activeListings ?? 0, icon: Building2, change: "+19%", changeType: "positive" },
    { label: "Total Bookings", value: isLoadingStats ? "..." : stats?.appointments ?? 0, icon: Calendar, change: "-2.5%", changeType: "negative" }
  ];
  
  const chartData = [
    { date: "Jan", sales: 2100 },
    { date: "Feb", sales: 3000 },
    { date: "Mar", sales: 2500 },
    { date: "Apr", sales: 4500 },
    { date: "May", sales: 4000 },
    { date: "Jun", sales: 6000 },
    { date: "Jul", sales: 5800 },
  ];
  
  const chartConfig = {
    sales: {
      label: "Sales",
      color: "hsl(var(--primary))",
    },
  } satisfies ChartConfig;

  const urgentTasks = (urgentTasksData || []).map((task, index) => ({
    id: task.id || index,
    task: task.appointment_type || task.notes || "Unnamed Task",
    time: formatDistanceToNow(new Date(task.scheduled_date), { addSuffix: true }),
  }));

  const triggerTestError = () => {
    throw new Error("Test error from Real Estate CRM Dashboard");
  };

  return (
    <div className="space-y-8 p-6 animate-slide-up">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="page-title">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back, here's your business overview.</p>
        </div>
        <button 
          onClick={triggerTestError}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          Test Sentry Error
        </button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {salesStats.map((stat, index) => (
          <Card 
            key={index} 
            className="bg-white/80 backdrop-blur-xl border border-blue-100 shadow-lg text-foreground"
          >
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-blue-700">{stat.label}</CardTitle>
              <stat.icon className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-900">{stat.value}</div>
              <p className={`text-xs ${stat.changeType === 'positive' ? 'text-green-500' : 'text-red-500'}`}>
                {stat.change} from last month
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        {/* Sales Analytics Chart */}
        <div className="lg:col-span-2">
          <Card className="bg-white/80 backdrop-blur-xl border border-blue-100 shadow-lg text-foreground">
            <CardHeader>
              <CardTitle className="text-blue-900">Sales Analytics</CardTitle>
              <CardDescription>An overview of your sales performance.</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-[300px] w-full">
                <LineChart data={chartData} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
                  <CartesianGrid vertical={false} strokeDasharray="3 3" stroke="hsl(var(--muted))" />
                  <XAxis dataKey="date" tickLine={false} axisLine={false} tick={{ fill: 'hsl(var(--muted-foreground))' }} />
                  <YAxis tickLine={false} axisLine={false} tick={{ fill: 'hsl(var(--muted-foreground))' }} tickFormatter={(value) => `$${value/1000}k`} />
                  <ChartTooltip
                    cursor={{ stroke: 'hsl(var(--border))', strokeWidth: 1, strokeDasharray: "3 3" }}
                    content={<ChartTooltipContent indicator="dot" />}
                  />
                  <Line type="monotone" dataKey="sales" stroke="hsl(var(--primary))" strokeWidth={2} dot={{r: 4, fill: "hsl(var(--primary))", stroke: "hsl(var(--background))", strokeWidth: 2 }} />
                </LineChart>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>

        {/* To-Do List */}
        <div className="space-y-6">
          <Card className="bg-white/80 backdrop-blur-xl border border-blue-100 shadow-lg text-foreground">
            <CardHeader>
              <CardTitle className="text-blue-900">To-Do List</CardTitle>
              <CardDescription>Your most urgent tasks. Check them off as you go!</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isLoadingTasks ? <p className="text-muted-foreground">Loading tasks...</p> : urgentTasks.length > 0 ? (
                urgentTasks.map((task, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    <Checkbox id={`task-${task.id || index}`} />
                    <div className="grid gap-1.5 leading-none">
                        <label htmlFor={`task-${task.id || index}`} className="text-sm font-medium text-foreground capitalize leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">{task.task}</label>
                        <p className="text-xs text-muted-foreground">{task.time}</p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-muted-foreground p-4 text-center">You're all caught up!</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
