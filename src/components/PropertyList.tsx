
import React from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
} from "@/components/ui/table";

const PLACEHOLDER_IMAGE = "/placeholder.svg";

async function fetchProperties() {
  const { data, error } = await supabase.from("properties").select("*").limit(25).order("created_at", { ascending: false });
  if (error) throw error;
  return data;
}

export function PropertyList() {
  const { data, isLoading, error } = useQuery({
    queryKey: ["properties"],
    queryFn: fetchProperties,
  });

  if (isLoading) return <div className="text-center py-8">Loading properties...</div>;
  if (error) return <div className="text-center py-8 text-red-500">Error loading properties.</div>;
  if (!data || data.length === 0) return <div className="text-center py-8 text-muted-foreground">No properties found.</div>;

  return (
    <div className="bg-white/70 rounded-xl border border-blue-100/70 shadow-sm p-4 mt-4 animate-fade-in">
      <Table>
        <TableCaption>Recent Properties</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Photo</TableHead>
            <TableHead>Address</TableHead>
            <TableHead>City</TableHead>
            <TableHead>Price</TableHead>
            <TableHead>Beds</TableHead>
            <TableHead>Baths</TableHead>
            <TableHead>Sq Ft</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((p: any) => (
            <TableRow key={p.id}>
              <TableCell>
                <img
                  src={p.photo_url ? p.photo_url : PLACEHOLDER_IMAGE}
                  alt={p.address_street_complete || "Property"}
                  className="w-20 h-16 object-cover rounded-lg border"
                  loading="lazy"
                  style={{ background: "#f5f5fa" }}
                />
              </TableCell>
              <TableCell>{p.address_street_complete || "—"}</TableCell>
              <TableCell>{p.address_city || "—"}</TableCell>
              <TableCell>{p.list_price ? `$${Number(p.list_price).toLocaleString()}` : "—"}</TableCell>
              <TableCell>{p.bedrooms_and_possible_bedrooms ?? "—"}</TableCell>
              <TableCell>{p.full_bathrooms ?? "—"}</TableCell>
              <TableCell>{p.square_footage ?? "—"}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

