
import React from 'react';
import { Bell, Search, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

export const Header: React.FC = () => {
  return (
    <header className="bg-white/60 backdrop-blur-xl border-b border-blue-100 px-6 py-4">
      <div className="flex items-center justify-between">
        
        {/* Search Bar */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
            <Input
              placeholder="Search anything..."
              className="pl-12 pr-4 py-3 bg-white/80 border-transparent rounded-lg text-base h-11 focus:border-primary focus:ring-primary/20 placeholder:text-muted-foreground"
            />
          </div>
        </div>
        
        {/* Right Side Actions */}
        <div className="flex items-center space-x-2 ml-6">
          
          {/* Notifications */}
          <Button variant="ghost" size="icon" className="relative text-muted-foreground hover:text-foreground hover:bg-blue-100 rounded-full h-10 w-10">
            <Bell className="h-5 w-5" />
            <Badge className="absolute top-1 right-1 h-2 w-2 bg-red-500 text-white p-0 flex items-center justify-center border-2 border-background rounded-full" />
          </Button>
          
          {/* Profile */}
          <Button variant="ghost" className="flex items-center space-x-3 hover:bg-blue-100 px-2 h-auto rounded-lg">
            <div className="h-10 w-10 bg-primary rounded-full flex items-center justify-center shadow-sm">
              <User className="h-5 w-5 text-primary-foreground" />
            </div>
            <div className="text-left hidden md:block">
              <div className="text-sm font-medium text-foreground">Sarah Chen</div>
              <div className="text-xs text-muted-foreground">Senior Agent</div>
            </div>
          </Button>
        </div>
      </div>
    </header>
  );
};
