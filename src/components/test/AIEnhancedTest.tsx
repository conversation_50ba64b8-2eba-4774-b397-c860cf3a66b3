import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { MessageRenderer } from '@/components/chat/MessageRenderer';
import { aiService } from '@/services/aiService';
import { 
  parseNoteAddIntent, 
  generateNoteConfirmation,
  saveClientNote,
  getClientNotes 
} from '@/utils/noteHandler';
import { 
  extractClientData, 
  createClient, 
  generateClientCreationConfirmation 
} from '@/utils/clientCreator';
import { realClientsData } from '@/data/realClientsData';
import { realPropertiesData } from '@/data/realPropertiesData';

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  details?: any;
}

export const AIEnhancedTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [testMessage, setTestMessage] = useState('');
  const [aiResponse, setAiResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const runAllTests = async () => {
    const results: TestResult[] = [];
    
    // Test 1: Advanced Client Search
    try {
      const searchResult = await aiService.sendMessage('Find buyers in Davis under $800k');
      results.push({
        name: 'Advanced Client Search',
        passed: searchResult.message.includes('client') || searchResult.message.includes('buyer'),
        message: 'Multi-criteria client search functionality',
        details: { intent: searchResult.intent, hasResults: searchResult.message.length > 50 }
      });
    } catch (error) {
      results.push({
        name: 'Advanced Client Search',
        passed: false,
        message: `Error: ${error}`,
      });
    }

    // Test 2: Note Adding Intent Detection
    const noteIntent = parseNoteAddIntent('Add note to Jason Smith: interested in larger yards');
    results.push({
      name: 'Note Intent Detection',
      passed: noteIntent !== null && noteIntent.clientName.includes('Jason'),
      message: 'Parse note-adding intent from natural language',
      details: noteIntent
    });

    // Test 3: Client Creation Data Extraction
    const clientData = extractClientData('Create client: Sarah Johnson, <EMAIL>, (555) 123-4567, buyer looking in Davis');
    results.push({
      name: 'Client Creation Extraction',
      passed: clientData !== null && clientData.firstName === 'Sarah' && clientData.lastName === 'Johnson',
      message: 'Extract client data from natural language',
      details: clientData
    });

    // Test 4: Property Compatibility Scoring
    if (realClientsData.length > 0 && realPropertiesData.length > 0) {
      try {
        // Access private method through any casting for testing
        const compatibility = (aiService as any).calculateClientPropertyMatch(
          realClientsData[0], 
          realPropertiesData[0]
        );
        results.push({
          name: 'Property Compatibility Scoring',
          passed: typeof compatibility.score === 'number' && compatibility.score >= 0,
          message: 'Calculate client-property compatibility scores',
          details: compatibility
        });
      } catch (error) {
        results.push({
          name: 'Property Compatibility Scoring',
          passed: false,
          message: `Error: ${error}`,
        });
      }
    }

    // Test 5: Relationship Insights Generation
    try {
      const insights = (aiService as any).generateInsights('Davis properties');
      results.push({
        name: 'Relationship Insights',
        passed: Array.isArray(insights) && insights.length > 0,
        message: 'Generate market and relationship insights',
        details: { insightCount: insights.length, insights: insights.slice(0, 3) }
      });
    } catch (error) {
      results.push({
        name: 'Relationship Insights',
        passed: false,
        message: `Error: ${error}`,
      });
    }

    // Test 6: Note Storage Integration
    if (realClientsData.length > 0) {
      const testClientId = realClientsData[0].id;
      const testNote = 'Test note from AI Enhanced Test Suite';
      
      const saveSuccess = saveClientNote(testClientId, testNote);
      const retrievedNotes = getClientNotes(testClientId);
      const noteExists = retrievedNotes.some(note => note.content === testNote);
      
      results.push({
        name: 'Note Storage Integration',
        passed: saveSuccess && noteExists,
        message: 'Save and retrieve client notes from localStorage',
        details: { saveSuccess, noteCount: retrievedNotes.length }
      });
    }

    // Test 7: Enhanced Search Performance
    const startTime = Date.now();
    try {
      await aiService.sendMessage('Show me all investors with high priority');
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      results.push({
        name: 'Search Performance',
        passed: responseTime < 3000, // Should respond within 3 seconds
        message: 'Enhanced search maintains good performance',
        details: { responseTime: `${responseTime}ms` }
      });
    } catch (error) {
      results.push({
        name: 'Search Performance',
        passed: false,
        message: `Error: ${error}`,
      });
    }

    setTestResults(results);
  };

  const testAIMessage = async () => {
    if (!testMessage.trim()) return;
    
    setIsLoading(true);
    try {
      const response = await aiService.sendMessage(testMessage);
      setAiResponse(response.message);
    } catch (error) {
      setAiResponse(`Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testScenarios = [
    'Find buyers in Davis under $800k',
    'Add note to Jason Smith: prefers modern homes',
    'Create client: John Doe, <EMAIL>, (555) 987-6543, investor',
    'Show me clients interested in Davis properties',
    'Find properties similar to 14446 Perimeter',
    'What insights do you have about my client portfolio?'
  ];

  const passedTests = testResults.filter(r => r.passed).length;
  const totalTests = testResults.length;

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>AI Enhanced Features Test Suite</CardTitle>
          <p className="text-sm text-muted-foreground">
            Comprehensive testing for advanced search, action handlers, and relationship intelligence
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <Button onClick={runAllTests} className="flex-1">
              Run All Tests
            </Button>
            {testResults.length > 0 && (
              <Badge variant={passedTests === totalTests ? "default" : "destructive"}>
                {passedTests}/{totalTests} Passed
              </Badge>
            )}
          </div>
          
          {testResults.length > 0 && (
            <div className="space-y-3">
              <h3 className="font-semibold">Test Results:</h3>
              {testResults.map((result, index) => (
                <div 
                  key={index} 
                  className={`p-4 rounded border ${result.passed ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <span className={`w-4 h-4 rounded-full ${result.passed ? 'bg-green-500' : 'bg-red-500'}`}></span>
                    <strong>{result.name}</strong>
                  </div>
                  <p className="text-sm mb-2">{result.message}</p>
                  {result.details && (
                    <details className="text-xs">
                      <summary className="cursor-pointer text-blue-600">View Details</summary>
                      <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto">
                        {JSON.stringify(result.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Interactive AI Testing</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Test Message:</label>
            <div className="flex gap-2">
              <Input
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                placeholder="Enter a message to test AI capabilities..."
                className="flex-1"
              />
              <Button onClick={testAIMessage} disabled={isLoading}>
                {isLoading ? 'Testing...' : 'Test'}
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            {testScenarios.map((scenario, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => setTestMessage(scenario)}
                className="text-left justify-start"
              >
                {scenario}
              </Button>
            ))}
          </div>
          
          {aiResponse && (
            <div>
              <label className="block text-sm font-medium mb-2">AI Response:</label>
              <div className="border rounded p-4 bg-gray-50">
                <MessageRenderer content={aiResponse} />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Feature Coverage Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded">
              <h4 className="font-semibold text-green-600">✅ Implemented</h4>
              <ul className="text-sm mt-2 space-y-1">
                <li>• Advanced multi-criteria search</li>
                <li>• Fuzzy name matching</li>
                <li>• Note adding workflow</li>
                <li>• Client creation system</li>
                <li>• Property compatibility scoring</li>
                <li>• Relationship insights</li>
                <li>• Smart link generation</li>
              </ul>
            </div>
            
            <div className="p-4 border rounded">
              <h4 className="font-semibold text-blue-600">📊 Metrics</h4>
              <ul className="text-sm mt-2 space-y-1">
                <li>• {realClientsData.length} clients in database</li>
                <li>• {realPropertiesData.length} properties available</li>
                <li>• Advanced search algorithms</li>
                <li>• Real-time insights generation</li>
                <li>• localStorage integration</li>
              </ul>
            </div>
            
            <div className="p-4 border rounded">
              <h4 className="font-semibold text-purple-600">🚀 Capabilities</h4>
              <ul className="text-sm mt-2 space-y-1">
                <li>• Natural language processing</li>
                <li>• Intent detection & parsing</li>
                <li>• Confirmation workflows</li>
                <li>• Error handling & validation</li>
                <li>• Performance optimization</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
