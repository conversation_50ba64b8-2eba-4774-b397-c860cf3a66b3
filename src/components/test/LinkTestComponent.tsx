import React from 'react';
import { MessageRenderer } from '@/components/chat/MessageRenderer';

export const LinkTestComponent: React.FC = () => {
  const testMessages = [
    {
      title: "Test 1: Client Link",
      content: "I found <PERSON>. [[View his profile|client:<PERSON>]] to see full details."
    },
    {
      title: "Test 2: Property Link",
      content: "Check out [[1447 Hawk Ct|property:1447 Hawk Ct]] for a great investment opportunity."
    },
    {
      title: "Test 3: Page Link",
      content: "You have appointments today. [[Check your schedule|page:schedule]] for details."
    },
    {
      title: "Test 4: Multiple Links",
      content: "Found 2 matches: [[<PERSON>|client:<PERSON>]] and [[<PERSON>|client:<PERSON>]]. [[View all clients|page:clients]] for more."
    }
  ];

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Link Parser Test</h1>
      
      <div className="space-y-6">
        {testMessages.map((test, index) => (
          <div key={index} className="border rounded-lg p-4">
            <h2 className="font-semibold mb-2">{test.title}</h2>
            <div className="bg-gray-50 p-3 rounded">
              <p className="text-sm text-gray-600 mb-2">Raw message:</p>
              <code className="text-xs bg-gray-200 p-1 rounded block mb-3">{test.content}</code>
              <p className="text-sm text-gray-600 mb-2">Rendered:</p>
              <MessageRenderer content={test.content} className="text-sm" />
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold mb-2">Debug Info:</h3>
        <p className="text-sm">This component tests if our link parser is working correctly.</p>
        <p className="text-sm">If you see clickable cyan buttons above, the parser is working!</p>
      </div>
    </div>
  );
};