import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertCircle, Play } from 'lucide-react';
import { aiService } from '@/services/aiService';
import { MessageRenderer } from '@/components/chat/MessageRenderer';
import { 
  parseNoteAddIntent, 
  saveClientNote, 
  getClientNotes 
} from '@/utils/noteHandler';
import { 
  extractClientData, 
  validateClientData 
} from '@/utils/clientCreator';
import { realClientsData } from '@/data/realClientsData';
import { realPropertiesData } from '@/data/realPropertiesData';

interface ValidationResult {
  test: string;
  status: 'pass' | 'fail' | 'warning' | 'pending';
  message: string;
  details?: any;
  timestamp?: Date;
}

export const ContainerValidationTest: React.FC = () => {
  const [results, setResults] = useState<ValidationResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [environmentInfo, setEnvironmentInfo] = useState<any>({});

  useEffect(() => {
    // Detect environment information
    setEnvironmentInfo({
      url: window.location.href,
      port: window.location.port,
      host: window.location.hostname,
      protocol: window.location.protocol,
      userAgent: navigator.userAgent.includes('Chrome') ? 'Chrome' : 'Other',
      timestamp: new Date().toISOString()
    });
  }, []);

  const addResult = (result: ValidationResult) => {
    setResults(prev => [...prev, { ...result, timestamp: new Date() }]);
  };

  const runContainerValidation = async () => {
    setIsRunning(true);
    setResults([]);

    // Test 1: Environment Detection
    addResult({
      test: 'Container Environment Detection',
      status: window.location.port === '5002' ? 'pass' : 'warning',
      message: window.location.port === '5002' 
        ? 'Running on correct Docker container port 5002' 
        : `Running on port ${window.location.port} - expected 5002`,
      details: environmentInfo
    });

    // Test 2: Data Availability
    try {
      const clientCount = realClientsData.length;
      const propertyCount = realPropertiesData.length;
      
      addResult({
        test: 'Mock Data Availability',
        status: clientCount > 0 && propertyCount > 0 ? 'pass' : 'fail',
        message: `${clientCount} clients, ${propertyCount} properties loaded`,
        details: { clientCount, propertyCount }
      });
    } catch (error) {
      addResult({
        test: 'Mock Data Availability',
        status: 'fail',
        message: `Data loading error: ${error}`,
      });
    }

    // Test 3: AI Service Initialization
    try {
      const testResponse = await aiService.sendMessage('test');
      addResult({
        test: 'AI Service Initialization',
        status: testResponse.message ? 'pass' : 'fail',
        message: testResponse.message ? 'AI service responding' : 'AI service not responding',
        details: { responseLength: testResponse.message.length, intent: testResponse.intent }
      });
    } catch (error) {
      addResult({
        test: 'AI Service Initialization',
        status: 'fail',
        message: `AI service error: ${error}`,
      });
    }

    // Test 4: Enhanced Search Capabilities
    try {
      const searchResponse = await aiService.sendMessage('Find buyers in Davis under $800k');
      const hasAdvancedFeatures = searchResponse.message.includes('client') || 
                                 searchResponse.message.includes('buyer') ||
                                 searchResponse.message.includes('Davis');
      
      addResult({
        test: 'Enhanced Search Capabilities',
        status: hasAdvancedFeatures ? 'pass' : 'warning',
        message: hasAdvancedFeatures ? 'Advanced search responding correctly' : 'Basic search only',
        details: { 
          responseLength: searchResponse.message.length,
          intent: searchResponse.intent,
          hasAdvancedFeatures 
        }
      });
    } catch (error) {
      addResult({
        test: 'Enhanced Search Capabilities',
        status: 'fail',
        message: `Search error: ${error}`,
      });
    }

    // Test 5: Note Adding Intent Detection
    try {
      const noteIntent = parseNoteAddIntent('Add note to Jason Smith: prefers modern homes');
      addResult({
        test: 'Note Adding Intent Detection',
        status: noteIntent ? 'pass' : 'fail',
        message: noteIntent ? `Detected note for ${noteIntent.clientName}` : 'Intent detection failed',
        details: noteIntent
      });
    } catch (error) {
      addResult({
        test: 'Note Adding Intent Detection',
        status: 'fail',
        message: `Intent detection error: ${error}`,
      });
    }

    // Test 6: Client Creation Data Extraction
    try {
      const clientData = extractClientData('Create client: John Doe, <EMAIL>, (555) 123-4567, buyer');
      const validationErrors = clientData ? validateClientData(clientData) : ['No data extracted'];
      
      addResult({
        test: 'Client Creation Data Extraction',
        status: clientData && validationErrors.length === 0 ? 'pass' : 'warning',
        message: clientData ? `Extracted: ${clientData.firstName} ${clientData.lastName}` : 'Extraction failed',
        details: { clientData, validationErrors }
      });
    } catch (error) {
      addResult({
        test: 'Client Creation Data Extraction',
        status: 'fail',
        message: `Extraction error: ${error}`,
      });
    }

    // Test 7: LocalStorage Integration
    try {
      const testClientId = realClientsData[0]?.id;
      if (testClientId) {
        const testNote = `Container validation test - ${Date.now()}`;
        const saveSuccess = saveClientNote(testClientId, testNote);
        const retrievedNotes = getClientNotes(testClientId);
        const noteExists = retrievedNotes.some(note => note.content === testNote);
        
        addResult({
          test: 'LocalStorage Integration',
          status: saveSuccess && noteExists ? 'pass' : 'fail',
          message: saveSuccess && noteExists ? 'Note storage working' : 'Storage failed',
          details: { saveSuccess, noteCount: retrievedNotes.length, noteExists }
        });
      } else {
        addResult({
          test: 'LocalStorage Integration',
          status: 'fail',
          message: 'No test client available',
        });
      }
    } catch (error) {
      addResult({
        test: 'LocalStorage Integration',
        status: 'fail',
        message: `Storage error: ${error}`,
      });
    }

    // Test 8: Smart Linking Integration
    try {
      const linkResponse = await aiService.sendMessage('Show me Jason Smith');
      const hasLinks = linkResponse.message.includes('[[') && linkResponse.message.includes(']]');
      
      addResult({
        test: 'Smart Linking Integration',
        status: hasLinks ? 'pass' : 'warning',
        message: hasLinks ? 'Smart links generated' : 'No smart links detected',
        details: { hasLinks, responseLength: linkResponse.message.length }
      });
    } catch (error) {
      addResult({
        test: 'Smart Linking Integration',
        status: 'fail',
        message: `Linking error: ${error}`,
      });
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: ValidationResult['status']) => {
    switch (status) {
      case 'pass': return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'fail': return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning': return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      default: return <Play className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: ValidationResult['status']) => {
    switch (status) {
      case 'pass': return 'bg-green-50 border-green-200';
      case 'fail': return 'bg-red-50 border-red-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  const passCount = results.filter(r => r.status === 'pass').length;
  const failCount = results.filter(r => r.status === 'fail').length;
  const warningCount = results.filter(r => r.status === 'warning').length;

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🐳 Container Environment Validation
            <Badge variant="outline">Docker Port 5002</Badge>
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Validates AI enhancements work correctly in the containerized environment
          </p>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Environment:</strong> {environmentInfo.url} | 
              <strong> Port:</strong> {environmentInfo.port} | 
              <strong> Expected:</strong> 5002
            </AlertDescription>
          </Alert>

          <div className="flex gap-4 mb-6">
            <Button 
              onClick={runContainerValidation} 
              disabled={isRunning}
              className="flex-1"
            >
              {isRunning ? 'Running Validation...' : 'Run Container Validation'}
            </Button>
            
            {results.length > 0 && (
              <div className="flex gap-2">
                <Badge variant="default">{passCount} Pass</Badge>
                <Badge variant="destructive">{failCount} Fail</Badge>
                <Badge variant="secondary">{warningCount} Warning</Badge>
              </div>
            )}
          </div>

          {results.length > 0 && (
            <div className="space-y-3">
              {results.map((result, index) => (
                <div 
                  key={index}
                  className={`p-4 rounded border ${getStatusColor(result.status)}`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    {getStatusIcon(result.status)}
                    <strong>{result.test}</strong>
                    <span className="text-xs text-gray-500">
                      {result.timestamp?.toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-sm mb-2">{result.message}</p>
                  {result.details && (
                    <details className="text-xs">
                      <summary className="cursor-pointer text-blue-600">View Details</summary>
                      <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto">
                        {JSON.stringify(result.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Live AI Testing (Container Environment)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Alert>
              <AlertDescription>
                Test the AI enhancements directly in the container environment at <strong>localhost:5002</strong>
              </AlertDescription>
            </Alert>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded">
                <h4 className="font-semibold mb-2">✅ Container Features</h4>
                <ul className="text-sm space-y-1">
                  <li>• Docker environment detection</li>
                  <li>• Port 5002 validation</li>
                  <li>• Real-time feature testing</li>
                  <li>• LocalStorage integration</li>
                  <li>• Error handling validation</li>
                </ul>
              </div>
              
              <div className="p-4 border rounded">
                <h4 className="font-semibold mb-2">🧪 Test Commands</h4>
                <ul className="text-sm space-y-1">
                  <li>• "Find buyers in Davis under $800k"</li>
                  <li>• "Add note to Jason: modern homes"</li>
                  <li>• "Create client: John Doe, buyer"</li>
                  <li>• "Show me market insights"</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
