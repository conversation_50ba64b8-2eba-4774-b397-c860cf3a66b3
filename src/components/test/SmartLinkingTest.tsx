import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageRenderer } from '@/components/chat/MessageRenderer';
import { 
  findClientRoute, 
  findPropertyRoute, 
  findScheduleRoute,
  parseEntityLinks,
  autoGenerateLinks,
  detectAndGenerateLinks
} from '@/utils/entityRouteMapper';
import { realClientsData } from '@/data/realClientsData';
import { realPropertiesData } from '@/data/realPropertiesData';

export const SmartLinkingTest: React.FC = () => {
  const [testResults, setTestResults] = useState<Array<{
    test: string;
    input: string;
    expected: string;
    actual: string;
    passed: boolean;
  }>>([]);

  const runTests = () => {
    const results: typeof testResults = [];

    // Test 1: Client name matching
    const testClient = realClientsData[0]; // <PERSON>
    const clientRoute = findClientRoute('<PERSON>');
    results.push({
      test: 'Exact client name match',
      input: '<PERSON>',
      expected: `/clients/${testClient.id}`,
      actual: clientRoute || 'null',
      passed: clientRoute === `/clients/${testClient.id}`
    });

    // Test 2: Partial client name matching
    const partialClientRoute = findClientRoute('Jason');
    const jasonClients = realClientsData.filter(c => c.first_name.toLowerCase() === 'jason');
    const expectedPartial = jasonClients.length === 1 ? `/clients/${jasonClients[0].id}` : null;
    results.push({
      test: 'Partial client name match (first name only)',
      input: 'Jason',
      expected: expectedPartial || 'null',
      actual: partialClientRoute || 'null',
      passed: partialClientRoute === expectedPartial
    });

    // Test 3: Property address matching
    const testProperty = realPropertiesData[0];
    const propertyRoute = findPropertyRoute(testProperty.address_street_complete);
    results.push({
      test: 'Exact property address match',
      input: testProperty.address_street_complete,
      expected: `/properties/${testProperty.id}`,
      actual: propertyRoute || 'null',
      passed: propertyRoute === `/properties/${testProperty.id}`
    });

    // Test 4: Schedule route
    const scheduleRoute = findScheduleRoute('appointment');
    results.push({
      test: 'Schedule route for appointment',
      input: 'appointment',
      expected: '/schedule',
      actual: scheduleRoute || 'null',
      passed: scheduleRoute === '/schedule'
    });

    // Test 5: Entity detection
    const testMessage = `I need to schedule a meeting with Jason Smith about the property at ${testProperty.address_street_complete}`;
    const { entities } = detectAndGenerateLinks(testMessage);
    const hasClient = entities.some(e => e.type === 'client' && e.value.includes('Jason'));
    const hasProperty = entities.some(e => e.type === 'property');
    const hasSchedule = entities.some(e => e.type === 'schedule');
    
    results.push({
      test: 'Entity detection in complex message',
      input: testMessage,
      expected: 'client, property, schedule entities detected',
      actual: `${hasClient ? 'client' : ''} ${hasProperty ? 'property' : ''} ${hasSchedule ? 'schedule' : ''}`.trim(),
      passed: hasClient && hasProperty && hasSchedule
    });

    // Test 6: Auto-link generation
    const autoLinkedMessage = autoGenerateLinks(testMessage);
    const hasLinks = autoLinkedMessage.includes('[[') && autoLinkedMessage.includes(']]');
    results.push({
      test: 'Auto-link generation',
      input: testMessage,
      expected: 'Message with [[link]] syntax',
      actual: hasLinks ? 'Links generated' : 'No links generated',
      passed: hasLinks
    });

    // Test 7: Link parsing and rendering
    const linkMessage = `Check out [[Jason's profile|client:Jason Smith]] and [[this property|property:${testProperty.address_street_complete}]]`;
    const { parsedMessage, links } = parseEntityLinks(linkMessage);
    results.push({
      test: 'Link parsing',
      input: linkMessage,
      expected: '2 links parsed',
      actual: `${links.length} links parsed`,
      passed: links.length === 2
    });

    setTestResults(results);
  };

  const renderTestMessage = (message: string) => {
    const autoLinked = autoGenerateLinks(message);
    return <MessageRenderer content={autoLinked} className="text-sm bg-gray-50 p-2 rounded" />;
  };

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Smart Page Linking Test Suite</CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={runTests} className="mb-4">
            Run All Tests
          </Button>
          
          {testResults.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-semibold">Test Results:</h3>
              {testResults.map((result, index) => (
                <div 
                  key={index} 
                  className={`p-3 rounded border ${result.passed ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}
                >
                  <div className="flex items-center gap-2">
                    <span className={`w-4 h-4 rounded-full ${result.passed ? 'bg-green-500' : 'bg-red-500'}`}></span>
                    <strong>{result.test}</strong>
                  </div>
                  <div className="mt-2 text-sm">
                    <div><strong>Input:</strong> {result.input}</div>
                    <div><strong>Expected:</strong> {result.expected}</div>
                    <div><strong>Actual:</strong> {result.actual}</div>
                  </div>
                </div>
              ))}
              
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <strong>Summary:</strong> {testResults.filter(r => r.passed).length} / {testResults.length} tests passed
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Live Demo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Sample AI Response with Auto-Generated Links:</h4>
            {renderTestMessage(`I found Jason Smith in your database. He's looking for properties in Davis with a budget around $2M. I also found Katherine Williams who might be interested in the property at ${realPropertiesData[0]?.address_street_complete}. Would you like to schedule a meeting?`)}
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">Manual Link Syntax Demo:</h4>
            <MessageRenderer 
              content="Here are your options: [[View Jason's profile|client:Jason Smith]], [[Check this property|property:14446 Perimeter]], and [[Go to Schedule|page:schedule]]"
              className="text-sm bg-gray-50 p-2 rounded"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
