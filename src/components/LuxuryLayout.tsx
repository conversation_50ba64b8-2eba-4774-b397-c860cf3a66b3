'use client';

import React from 'react';
import { LuxurySidebar } from './LuxurySidebar';
import { LuxuryHeader } from './LuxuryHeader';

interface LuxuryLayoutProps {
  children: React.ReactNode;
}

export const LuxuryLayout: React.FC<LuxuryLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-luxury-cream">
      <div className="flex h-screen">
        <LuxurySidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <LuxuryHeader />
          <main className="flex-1 overflow-auto p-6">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
};