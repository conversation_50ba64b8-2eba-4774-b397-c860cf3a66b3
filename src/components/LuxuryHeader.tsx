'use client';

import React from 'react';
import { Bell, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { GlobalSearch } from '@/components/GlobalSearch';

export const LuxuryHeader: React.FC = () => {
  return (
    <header className="h-16 glass-card border-b border-luxury-gold/20 px-6 flex items-center justify-between relative z-10">
      {/* Search Bar */}
      <div className="flex-1 max-w-xl">
        <GlobalSearch />
      </div>

      {/* Right Section */}
      <div className="flex items-center gap-4">
        {/* Notifications */}
        <Button variant="ghost" size="icon" className="relative rounded-xl hover:bg-white/60">
          <Bell className="h-5 w-5 text-luxury-navy/70" />
          <span className="absolute -top-1 -right-1 h-3 w-3 bg-luxury-gold rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-bold">3</span>
          </span>
        </Button>

        {/* User Profile */}
        <Button variant="ghost" className="rounded-xl hover:bg-white/60 gap-2">
          <div className="h-8 w-8 bg-gradient-to-br from-luxury-navy to-luxury-navy-light rounded-full flex items-center justify-center">
            <User className="h-4 w-4 text-white" />
          </div>
          <div className="text-left hidden sm:block">
            <div className="text-sm font-medium text-luxury-navy">Sarah Johnson</div>
            <div className="text-xs text-luxury-navy/60">Real Estate Agent</div>
          </div>
        </Button>
      </div>
    </header>
  );
};