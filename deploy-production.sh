#!/bin/bash

# Production Deployment Script
# Builds the app with AI integration disabled for public deployment

echo "🚀 Building for production deployment..."
echo "📝 AI Integration: DISABLED (demo mode only)"
echo "🔑 API Keys: NOT INCLUDED"
echo ""

# Set production environment
export NODE_ENV=production

# Build with production settings (AI disabled)
npm run build

echo ""
echo "✅ Production build complete!"
echo "📁 Build output: ./dist/"
echo ""
echo "🔧 Production Configuration:"
echo "   • AI Chat: Demo mode only"
echo "   • API Calls: Disabled"
echo "   • Features: All UI features functional"
echo "   • Data: Local mock data only"
echo ""
echo "📦 Ready to deploy to Vercel!"
echo "   Next step: vercel --prod"