const fs = require('fs');
const path = require('path');

// Read the MLS data
const mlsData = fs.readFileSync('Listing.txt', 'utf8');
const lines = mlsData.split('\n');
const headers = lines[0].split('\t');

// Parse each property
const properties = [];
for (let i = 1; i < lines.length && i <= 100; i++) { // Limit to 100 properties for manageable size
  const values = lines[i].split('\t');
  if (values.length < headers.length) continue;
  
  const property = {};
  
  // Map the data to our property structure
  property.id = 100000 + i; // Generate unique IDs
  property.address_street_complete = values[headers.indexOf('Address - Street Complete')] || '';
  property.address_city = values[headers.indexOf('Address - City')] || '';
  property.address_zip_code = values[headers.indexOf('Address - Zip Code')] || '';
  property.list_price = values[headers.indexOf('List Price')] || '0';
  property.square_footage = parseInt(values[headers.indexOf('Square Footage')] || '0');
  property.bedrooms_and_possible_bedrooms = parseInt(values[headers.indexOf('Bedrooms And Possible Bedrooms')] || '0');
  property.full_bathrooms = values[headers.indexOf('Full Bathrooms')] || '0';
  property.partial_bathrooms = values[headers.indexOf('Partial Bathrooms')] || '0';
  property.garage_spaces = parseInt(values[headers.indexOf('Garage Spaces')] || '0');
  property.lot_size_acres = values[headers.indexOf('Lot Size - Acres')] || '0';
  property.year_built_details = values[headers.indexOf('Year Built Details')] || '';
  property.architectural_style = values[headers.indexOf('Architectural Style')] || '';
  property.property_type = values[headers.indexOf('Property Type')] || 'Residential';
  property.status = values[headers.indexOf('Status')] || 'Active';
  property.dom = parseInt(values[headers.indexOf('DOM')] || '0');
  property.public_remarks = values[headers.indexOf('Public Remarks')] || '';
  property.listing_date = values[headers.indexOf('Listing Date')] || '';
  property.subdivision = values[headers.indexOf('Subdivision')] || '';
  property.listing_number = values[headers.indexOf('Listing Number')] || '';
  property.original_price = values[headers.indexOf('Original Price')] || property.list_price;
  property.pending_date = values[headers.indexOf('Pending Date')] || null;
  property.close_date = values[headers.indexOf('Close Date')] || null;
  property.close_price = values[headers.indexOf('Close Price')] || '0';
  property.cooling = values[headers.indexOf('Cooling')] || '';
  property.heating = values[headers.indexOf('Heating')] || '';
  property.fireplace_features = values[headers.indexOf('Fireplace Features')] || '';
  property.parking_features = values[headers.indexOf('Parking Features')] || '';
  property.exterior_features = values[headers.indexOf('Exterior Features')] || '';
  property.pool = values[headers.indexOf('Pool')] || 'No';
  property.pool_features = values[headers.indexOf('Pool Features')] || '';
  
  // Generate photo URL - check if the image file exists
  const photoFileName = `${property.listing_number}_1.jpg`;
  if (fs.existsSync(photoFileName)) {
    property.photo_url = `/${photoFileName}`;
  } else {
    property.photo_url = '/placeholder.svg';
  }
  
  properties.push(property);
}

// Create the TypeScript file content
const tsContent = `// Real property data from MLS
// Generated on ${new Date().toISOString().split('T')[0]}

export interface Property {
  id: number;
  address_street_complete: string;
  address_city: string;
  address_zip_code: string;
  list_price: string;
  square_footage: number;
  bedrooms_and_possible_bedrooms: number;
  full_bathrooms: string;
  partial_bathrooms: string;
  garage_spaces: number;
  lot_size_acres: string;
  year_built_details: string;
  architectural_style: string;
  property_type: string;
  status: string;
  dom: number;
  public_remarks: string;
  photo_url: string;
  listing_date: string;
  subdivision: string;
  listing_number: string;
  original_price: string;
  pending_date: string | null;
  close_date: string | null;
  close_price: string;
  cooling: string;
  heating: string;
  fireplace_features: string;
  parking_features: string;
  exterior_features: string;
  pool: string;
  pool_features: string;
}

export const realPropertiesData: Property[] = ${JSON.stringify(properties, null, 2)};
`;

// Write the TypeScript file
fs.writeFileSync(path.join('..', 'src', 'data', 'realPropertiesData.ts'), tsContent);

console.log(`Created realPropertiesData.ts with ${properties.length} properties`);
console.log('Photos are referenced from the public directory');