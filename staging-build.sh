#!/bin/bash
# Build the staging container with current code

echo "🏗️  Building staging container..."
echo "This will create a self-contained snapshot of your current code"
echo ""

# Build the container
docker build -f Dockerfile.staging -t lovable-crm-staging:latest .

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Staging container built successfully!"
    echo ""
    echo "To run it: ./staging-run.sh"
    echo "Or manually: docker run -p 5003:4173 lovable-crm-staging:latest"
else
    echo "❌ Build failed!"
    exit 1
fi