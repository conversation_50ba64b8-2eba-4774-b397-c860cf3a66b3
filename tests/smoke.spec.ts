import { test, expect } from '@playwright/test';

test.describe('CRM Smoke Tests', () => {
  
  test('Homepage loads successfully', async ({ page }) => {
    await page.goto('/');
    
    // Basic page load verification
    await expect(page).toHaveTitle(/<PERSON><PERSON>/);
    await expect(page.locator('h1')).toBeVisible();
  });

  test('Navigation menu works', async ({ page }) => {
    await page.goto('/');
    
    // Test main navigation links
    const navLinks = ['Dashboard', 'Clients', 'Properties', 'Schedule', 'Reports'];
    
    for (const link of navLinks) {
      await page.click(`text=${link}`);
      await expect(page).toHaveURL(new RegExp(link.toLowerCase()));
      await expect(page.locator('h1, h2')).toBeVisible();
    }
  });

  test('CRM responds within acceptable time', async ({ page }) => {
    const startTime = Date.now();
    await page.goto('/dashboard');
    
    // Wait for main content to load
    await expect(page.locator('[data-testid="dashboard-content"], .dashboard, main')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    console.log(`Dashboard loaded in ${loadTime}ms`);
    
    // Should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);
  });
});