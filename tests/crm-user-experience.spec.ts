import { test, expect } from '@playwright/test';

test.describe('Real Estate CRM - Complete User Experience', () => {
  
  test.beforeEach(async ({ page }) => {
    // Start fresh for each test
    await page.goto('/');
  });

  test('Homepage loads and AI chat is functional', async ({ page }) => {
    // Check homepage loads
    await expect(page.locator('h1')).toContainText('Welcome to Narissa Realty');
    
    // Verify AI chat is present
    await expect(page.locator('[data-testid="ai-chat"]')).toBeVisible();
    
    // Test AI chat interaction
    await page.fill('[data-testid="chat-input"]', 'Find clients in Davis');
    await page.click('[data-testid="send-button"]');
    
    // Wait for AI response
    await expect(page.locator('[data-testid="chat-messages"]')).toContainText('Found');
  });

  test('Dashboard navigation and metrics display', async ({ page }) => {
    // Navigate to dashboard
    await page.click('text=Dashboard');
    await expect(page).toHaveURL(/.*dashboard/);
    
    // Check dashboard metrics are displayed
    await expect(page.locator('[data-testid="total-properties"]')).toBeVisible();
    await expect(page.locator('[data-testid="active-clients"]')).toBeVisible();
    await expect(page.locator('[data-testid="pending-transactions"]')).toBeVisible();
    
    // Verify charts are rendered
    await expect(page.locator('.recharts-wrapper')).toBeVisible();
  });

  test('Client management workflow', async ({ page }) => {
    // Navigate to clients page
    await page.click('text=Clients');
    await expect(page).toHaveURL(/.*clients/);
    
    // Verify clients list loads
    await expect(page.locator('[data-testid="client-list"]')).toBeVisible();
    
    // Test client search
    await page.fill('[data-testid="client-search"]', 'Aaron');
    await expect(page.locator('text=Aaron')).toBeVisible();
    
    // Click on first client
    await page.click('[data-testid="client-card"]:first-child');
    
    // Verify client dossier loads
    await expect(page.locator('[data-testid="client-details"]')).toBeVisible();
    await expect(page.locator('[data-testid="client-notes"]')).toBeVisible();
  });

  test('Property search and filtering', async ({ page }) => {
    // Navigate to properties
    await page.click('text=Properties');
    await expect(page).toHaveURL(/.*properties/);
    
    // Verify properties grid loads
    await expect(page.locator('[data-testid="properties-grid"]')).toBeVisible();
    
    // Test price filter
    await page.click('[data-testid="price-filter"]');
    await page.selectOption('[data-testid="max-price"]', '800000');
    await page.click('[data-testid="apply-filters"]');
    
    // Verify filtered results
    await expect(page.locator('[data-testid="property-card"]')).toBeVisible();
    
    // Test property detail view
    await page.click('[data-testid="property-card"]:first-child');
    await expect(page.locator('[data-testid="property-gallery"]')).toBeVisible();
    await expect(page.locator('[data-testid="property-specs"]')).toBeVisible();
  });

  test('Global search functionality', async ({ page }) => {
    // Test global search shortcut
    await page.keyboard.press('Meta+k'); // Cmd+K
    await expect(page.locator('[data-testid="global-search"]')).toBeVisible();
    
    // Search for a client
    await page.fill('[data-testid="search-input"]', 'John');
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
    
    // Verify search results include clients and properties
    await expect(page.locator('text=Clients')).toBeVisible();
    await expect(page.locator('text=Properties')).toBeVisible();
  });

  test('AI assistant interactions', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Open AI chat
    await page.click('[data-testid="ai-chat-toggle"]');
    
    // Test various AI commands
    const testQueries = [
      'Show me buyers under $800k',
      'Find properties in Davis',
      'Add note to Aaron: prefers modern homes',
      'Create client John Doe'
    ];
    
    for (const query of testQueries) {
      await page.fill('[data-testid="chat-input"]', query);
      await page.click('[data-testid="send-button"]');
      
      // Wait for response
      await page.waitForSelector('[data-testid="chat-messages"] .message:last-child', {
        timeout: 10000
      });
      
      // Verify response is not empty
      const lastMessage = page.locator('[data-testid="chat-messages"] .message:last-child');
      await expect(lastMessage).not.toBeEmpty();
    }
  });

  test('Schedule and appointments', async ({ page }) => {
    await page.click('text=Schedule');
    await expect(page).toHaveURL(/.*schedule/);
    
    // Verify calendar loads
    await expect(page.locator('[data-testid="calendar"]')).toBeVisible();
    
    // Check appointments are displayed
    await expect(page.locator('[data-testid="appointment-card"]')).toBeVisible();
    
    // Test date navigation
    await page.click('[data-testid="next-week"]');
    await page.click('[data-testid="prev-week"]');
  });

  test('Reports and analytics', async ({ page }) => {
    await page.click('text=Reports');
    await expect(page).toHaveURL(/.*reports/);
    
    // Verify report sections load
    await expect(page.locator('[data-testid="overview-report"]')).toBeVisible();
    await expect(page.locator('[data-testid="client-analytics"]')).toBeVisible();
    await expect(page.locator('[data-testid="property-analytics"]')).toBeVisible();
    
    // Test report type switching
    await page.click('[data-testid="clients-tab"]');
    await expect(page.locator('[data-testid="client-distribution-chart"]')).toBeVisible();
    
    await page.click('[data-testid="properties-tab"]');
    await expect(page.locator('[data-testid="property-value-chart"]')).toBeVisible();
  });

  test('Mobile responsiveness', async ({ page, isMobile }) => {
    test.skip(!isMobile, 'Mobile-specific test');
    
    await page.goto('/');
    
    // Check mobile navigation
    await page.click('[data-testid="mobile-menu-toggle"]');
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
    
    // Test mobile client list
    await page.click('text=Clients');
    await expect(page.locator('[data-testid="client-list"]')).toBeVisible();
    
    // Verify mobile-optimized layout
    const clientCard = page.locator('[data-testid="client-card"]:first-child');
    await expect(clientCard).toBeVisible();
  });

  test('Performance and loading states', async ({ page }) => {
    // Test page load performance
    const navigationPromise = page.goto('/dashboard');
    
    // Check for loading states
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
    
    await navigationPromise;
    
    // Verify content loads within reasonable time
    await expect(page.locator('[data-testid="dashboard-content"]')).toBeVisible();
    
    // Test navigation performance
    const startTime = Date.now();
    await page.click('text=Properties');
    await expect(page.locator('[data-testid="properties-grid"]')).toBeVisible();
    const loadTime = Date.now() - startTime;
    
    // Should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);
  });

  test('Error handling and edge cases', async ({ page }) => {
    // Test navigation to non-existent route
    await page.goto('/nonexistent');
    await expect(page.locator('text=404')).toBeVisible();
    
    // Test back to home
    await page.click('text=Home');
    await expect(page).toHaveURL('/');
    
    // Test empty search results
    await page.goto('/properties');
    await page.fill('[data-testid="search-input"]', 'nonexistentproperty123');
    await expect(page.locator('text=No properties found')).toBeVisible();
  });

  test('Data persistence and localStorage', async ({ page }) => {
    // Navigate to client dossier
    await page.goto('/clients');
    await page.click('[data-testid="client-card"]:first-child');
    
    // Add a note
    const testNote = `Test note added at ${Date.now()}`;
    await page.fill('[data-testid="notes-textarea"]', testNote);
    await page.click('[data-testid="save-notes"]');
    
    // Verify success message
    await expect(page.locator('text=Notes saved')).toBeVisible();
    
    // Refresh page and verify note persists
    await page.reload();
    await expect(page.locator('[data-testid="notes-textarea"]')).toHaveValue(testNote);
  });
});

test.describe('CRM Workflow Integration Tests', () => {
  
  test('End-to-end property matching workflow', async ({ page }) => {
    // Start from dashboard
    await page.goto('/dashboard');
    
    // Use AI to find client-property matches
    await page.click('[data-testid="ai-chat-toggle"]');
    await page.fill('[data-testid="chat-input"]', 'Show me clients interested in Davis properties under $800k');
    await page.click('[data-testid="send-button"]');
    
    // Wait for AI response
    await page.waitForSelector('[data-testid="chat-messages"] .message:last-child');
    
    // Navigate to suggested client
    await page.click('text=View Client');
    
    // Verify property matches are shown
    await expect(page.locator('[data-testid="property-matches"]')).toBeVisible();
    
    // Click on a matched property
    await page.click('[data-testid="matched-property"]:first-child');
    
    // Verify property details load
    await expect(page.locator('[data-testid="property-details"]')).toBeVisible();
  });

  test('Complete client onboarding flow', async ({ page }) => {
    // Use AI to create new client
    await page.goto('/');
    await page.fill('[data-testid="chat-input"]', 'Create client: Jane Smith, <EMAIL>, buyer, budget $750k');
    await page.click('[data-testid="send-button"]');
    
    // Wait for confirmation
    await expect(page.locator('text=Client created successfully')).toBeVisible();
    
    // Navigate to clients list
    await page.click('text=Clients');
    
    // Verify new client appears
    await expect(page.locator('text=Jane Smith')).toBeVisible();
    
    // Open client dossier
    await page.click('text=Jane Smith');
    
    // Add initial notes
    await page.fill('[data-testid="notes-textarea"]', 'New client - looking for family home in Davis area');
    await page.click('[data-testid="save-notes"]');
    
    // Verify client is fully set up
    await expect(page.locator('text=Notes saved')).toBeVisible();
  });
});